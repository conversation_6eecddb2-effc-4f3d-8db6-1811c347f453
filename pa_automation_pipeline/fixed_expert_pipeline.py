#!/usr/bin/env python3
"""
Fixed Expert PA Pipeline - Uses actual PDF field names instead of guessing
Implements the immediate fix with proper field validation and mapping.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any

from src.form_filler import fill_pa_form
from expert_medical_extractor import ExpertMedicalExtractor
from pdf_field_analyzer import PDFFieldAnalyzer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedExpertPAPipeline:
    """Fixed PA automation pipeline that reads actual PDF fields first."""
    
    def __init__(self):
        self.medical_extractor = ExpertMedicalExtractor()
        self.field_analyzer = PDFFieldAnalyzer()
    
    def run_fixed_pipeline(self, patient_name: str, pa_form_path: Path, referral_path: Path, output_dir: Path) -> Dict[str, Any]:
        """
        Run the fixed pipeline that reads actual PDF fields first.
        
        Args:
            patient_name: Patient identifier
            pa_form_path: Path to blank PA form  
            referral_path: Path to referral package
            output_dir: Directory for outputs
            
        Returns:
            Pipeline results with accurate success reporting
        """
        
        logger.info(f"🔧 STARTING FIXED EXPERT PIPELINE FOR {patient_name.upper()}")
        logger.info("=" * 80)
        
        results = {
            "patient_name": patient_name,
            "field_analysis": {},
            "data_extraction": {},
            "field_mapping": {},
            "form_filling": {},
            "overall_success": False
        }
        
        try:
            # Step 1: Read actual PDF field names first (no more guessing!)
            logger.info("📋 STEP 1: Reading actual PDF field names using pypdf...")
            actual_fields = self.field_analyzer.read_actual_pdf_fields(pa_form_path)
            
            if not actual_fields.get("analysis_success", False):
                error_msg = actual_fields.get("error", "Field analysis failed")
                logger.error(f"❌ PDF field analysis failed: {error_msg}")
                results["field_analysis"] = {"status": "FAILED", "error": error_msg}
                return results
            
            total_pdf_fields = actual_fields.get("total_fields", 0)
            pdf_field_names = list(actual_fields.get("fields", {}).keys())
            
            logger.info(f"✅ Found {total_pdf_fields} actual PDF fields")
            logger.info(f"📝 Sample fields: {pdf_field_names[:5]}")
            
            results["field_analysis"] = {
                "status": "SUCCESS",
                "total_pdf_fields": total_pdf_fields,
                "field_names": pdf_field_names[:10],  # First 10 for brevity
                "analysis_file": str(output_dir / f"{patient_name}_actual_fields.json")
            }
            
            # Save actual field analysis
            with open(output_dir / f"{patient_name}_actual_fields.json", 'w') as f:
                json.dump(actual_fields, f, indent=2)
            
            # Step 2: Extract data using medical extractor with ACTUAL field names
            logger.info("🔬 STEP 2: Expert medical data extraction with real field schema...")
            
            # Create schema using actual PDF field names
            real_field_schema = {
                "fields": {}
            }
            
            for field_name in pdf_field_names:
                real_field_schema["fields"][field_name] = {
                    "semantic_meaning": self._infer_semantic_meaning(field_name),
                    "field_type": "text",
                    "extraction_guidance": f"Extract data for PDF field: {field_name}"
                }
            
            extraction_result = self.medical_extractor.extract_medical_data(referral_path, real_field_schema)
            
            if 'error' in extraction_result:
                logger.error(f"❌ Medical extraction failed: {extraction_result['error']}")
                results["data_extraction"]["status"] = "FAILED"
                return results
            
            extracted_data = extraction_result['extracted_data']
            extraction_rate = extraction_result['extraction_rate']
            
            # Save extracted data
            extraction_path = output_dir / f"{patient_name}_fixed_extracted.json"
            with open(extraction_path, 'w') as f:
                json.dump(extraction_result, f, indent=2)
            
            logger.info(f"✅ Medical extraction: {extraction_rate} extraction rate")
            results["data_extraction"] = {
                "status": "SUCCESS",
                "extraction_rate": extraction_rate,
                "fields_extracted": extraction_result['extraction_count'],
                "total_fields": extraction_result['total_fields'],
                "data_path": str(extraction_path)
            }
            
            # Step 3: Validate field mapping (ensure extracted fields exist in PDF)
            logger.info("🔍 STEP 3: Validating field mapping...")
            
            field_mapping = self.field_analyzer.create_field_mapping(actual_fields, extracted_data)
            mapping_validation = self.field_analyzer.validate_field_mapping(field_mapping, actual_fields)
            
            logger.info(f"📊 Field mapping validation: {mapping_validation['validation_rate']} success rate")
            logger.info(f"✅ Valid mappings: {mapping_validation['valid_mappings']}")
            logger.info(f"❌ Invalid mappings: {mapping_validation['invalid_mappings']}")
            
            results["field_mapping"] = {
                "status": "SUCCESS",
                "total_mappings": mapping_validation['total_mappings'],
                "valid_mappings": mapping_validation['valid_mappings'],
                "invalid_mappings": mapping_validation['invalid_mappings'],
                "validation_rate": mapping_validation['validation_rate']
            }
            
            # Step 4: Fill form with validated mapping
            logger.info("📝 STEP 4: Filling PA form with validated field mapping...")
            filled_pdf_path = output_dir / f"{patient_name}_FIXED_FILLED.pdf"
            
            # Use only valid mappings for filling
            valid_mappings = mapping_validation['valid_fields']
            
            # Create schema for form filler using valid mappings
            form_schema = {}
            fillable_data = {}
            
            for semantic_key, pdf_field_name in valid_mappings.items():
                if semantic_key in extracted_data and extracted_data[semantic_key] is not None:
                    # Use the actual PDF field name as the key
                    form_schema[pdf_field_name] = {
                        "acro_id": pdf_field_name,
                        "semantic_meaning": pdf_field_name,  # No conversion needed
                        "type": "text",
                        "human_name": semantic_key
                    }
                    fillable_data[pdf_field_name] = extracted_data[semantic_key]
            
            logger.info(f"📊 Attempting to fill {len(fillable_data)} validated fields...")
            
            # Fill the form
            fill_success = fill_pa_form(
                blank_pdf_path=pa_form_path,
                schema=form_schema,
                extracted_data=fillable_data,
                output_path=filled_pdf_path
            )
            
            if fill_success:
                actual_filled_count = len([v for v in fillable_data.values() if v is not None])
                total_possible_fields = len(actual_fields.get("fields", {}))
                actual_fill_rate = f"{actual_filled_count}/{total_possible_fields}"
                
                logger.info(f"✅ Form filling: {actual_fill_rate} fields actually filled")
                results["form_filling"] = {
                    "status": "SUCCESS",
                    "fields_actually_filled": actual_filled_count,
                    "total_pdf_fields": total_possible_fields,
                    "actual_fill_rate": actual_fill_rate,
                    "output_path": str(filled_pdf_path),
                    "filled_fields": list(fillable_data.keys())
                }
                results["overall_success"] = True
            else:
                logger.error("❌ Form filling failed")
                results["form_filling"]["status"] = "FAILED"
                return results
            
            # Generate comprehensive report
            self._generate_fixed_pipeline_report(results, output_dir, patient_name)
            
            logger.info("=" * 80)
            logger.info(f"🎉 FIXED PIPELINE COMPLETED FOR {patient_name.upper()}")
            logger.info(f"📄 Filled form: {filled_pdf_path}")
            logger.info(f"📊 Actual fill rate: {actual_fill_rate}")
            logger.info("=" * 80)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Fixed pipeline failed for {patient_name}: {e}")
            results["error"] = str(e)
            return results
    
    def _infer_semantic_meaning(self, field_name: str) -> str:
        """Infer semantic meaning from PDF field name."""
        
        field_lower = field_name.lower()
        
        # Common mappings
        if field_name == "T11":
            return "patient_first_name"
        elif field_name == "T12":
            return "patient_last_name"
        elif field_name == "T13":
            return "patient_dob"
        elif field_name == "T14":
            return "member_id"
        elif field_name == "T15":
            return "diagnosis"
        elif field_name == "T16":
            return "drug_name"
        elif field_name == "T17":
            return "dosage"
        elif "request by" in field_lower:
            return "prescriber_name"
        elif "phone" in field_lower:
            return "prescriber_phone"
        elif "fax" in field_lower:
            return "prescriber_fax"
        elif "insurance" in field_lower:
            return "insurance_info"
        else:
            return field_name.lower().replace(" ", "_")
    
    def _generate_fixed_pipeline_report(self, results: Dict[str, Any], output_dir: Path, patient_name: str):
        """Generate comprehensive fixed pipeline report."""
        
        report_path = output_dir / f"{patient_name}_FIXED_PIPELINE_REPORT.md"
        
        with open(report_path, 'w') as f:
            f.write(f"# Fixed Expert PA Pipeline Report - {patient_name}\n\n")
            
            f.write("## Pipeline Summary\n\n")
            f.write(f"- **Patient**: {patient_name}\n")
            f.write(f"- **Overall Success**: {'✅ YES' if results['overall_success'] else '❌ NO'}\n")
            f.write(f"- **Pipeline Type**: Fixed (reads actual PDF fields)\n\n")
            
            f.write("## Step Results\n\n")
            
            # Field analysis
            field_analysis = results.get("field_analysis", {})
            f.write("### 1. PDF Field Analysis (NEW)\n")
            f.write(f"- **Status**: {field_analysis.get('status', 'Unknown')}\n")
            f.write(f"- **Total PDF Fields Found**: {field_analysis.get('total_pdf_fields', 0)}\n")
            f.write(f"- **Field Names**: {', '.join(field_analysis.get('field_names', []))}\n\n")
            
            # Data extraction
            extraction = results.get("data_extraction", {})
            f.write("### 2. Expert Medical Extraction\n")
            f.write(f"- **Status**: {extraction.get('status', 'Unknown')}\n")
            f.write(f"- **Extraction Rate**: {extraction.get('extraction_rate', 'N/A')}\n")
            f.write(f"- **Fields Extracted**: {extraction.get('fields_extracted', 0)}/{extraction.get('total_fields', 0)}\n\n")
            
            # Field mapping validation
            mapping = results.get("field_mapping", {})
            f.write("### 3. Field Mapping Validation (NEW)\n")
            f.write(f"- **Status**: {mapping.get('status', 'Unknown')}\n")
            f.write(f"- **Validation Rate**: {mapping.get('validation_rate', 'N/A')}\n")
            f.write(f"- **Valid Mappings**: {mapping.get('valid_mappings', 0)}\n")
            f.write(f"- **Invalid Mappings**: {mapping.get('invalid_mappings', 0)}\n\n")
            
            # Form filling
            filling = results.get("form_filling", {})
            f.write("### 4. Form Filling\n")
            f.write(f"- **Status**: {filling.get('status', 'Unknown')}\n")
            f.write(f"- **Actual Fill Rate**: {filling.get('actual_fill_rate', 'N/A')}\n")
            f.write(f"- **Fields Actually Filled**: {filling.get('fields_actually_filled', 0)}\n")
            f.write(f"- **Total PDF Fields**: {filling.get('total_pdf_fields', 0)}\n")
            f.write(f"- **Filled Field Names**: {', '.join(filling.get('filled_fields', []))}\n\n")
            
            if results['overall_success']:
                f.write("## ✅ Fixed Pipeline Success\n\n")
                f.write("The fixed PA automation pipeline completed successfully with:\n")
                f.write("- ✅ Actual PDF field discovery (no more guessing)\n")
                f.write("- ✅ Validated field mapping\n") 
                f.write("- ✅ Accurate fill rate reporting\n")
                f.write("- ✅ Real data in correct PDF fields\n")
            else:
                f.write("## ❌ Pipeline Issues\n\n")
                f.write("The fixed pipeline encountered issues. Check individual step statuses above.\n")
        
        logger.info(f"📊 Fixed pipeline report saved: {report_path}")

def run_fixed_akshay():
    """Run fixed pipeline for Akshay."""
    
    pipeline = FixedExpertPAPipeline()
    
    pa_form = Path("Input Data/Akshay/pa.pdf")
    referral = Path("Input Data/Akshay/referral_package.pdf")
    output_dir = Path("fixed_output")
    output_dir.mkdir(exist_ok=True)
    
    if not pa_form.exists() or not referral.exists():
        logger.error(f"❌ Missing files for Akshay:")
        logger.error(f"   PA form: {pa_form} ({'exists' if pa_form.exists() else 'missing'})")
        logger.error(f"   Referral: {referral} ({'exists' if referral.exists() else 'missing'})")
        return None
    
    return pipeline.run_fixed_pipeline("Akshay", pa_form, referral, output_dir)

def run_fixed_abdullah():
    """Run fixed pipeline for Abdullah (if PDF is not encrypted)."""
    
    pipeline = FixedExpertPAPipeline()
    
    pa_form = Path("Input Data/Adbulla/PA.pdf")
    referral = Path("Input Data/Adbulla/referral_package.pdf")
    output_dir = Path("fixed_output")
    output_dir.mkdir(exist_ok=True)
    
    if not pa_form.exists() or not referral.exists():
        logger.error(f"❌ Missing files for Abdullah:")
        logger.error(f"   PA form: {pa_form} ({'exists' if pa_form.exists() else 'missing'})")
        logger.error(f"   Referral: {referral} ({'exists' if referral.exists() else 'missing'})")
        return None
    
    return pipeline.run_fixed_pipeline("Abdullah", pa_form, referral, output_dir)

def run_fixed_pipelines():
    """Run fixed pipeline for both patients."""
    
    print("\n" + "=" * 100)
    print("🔧 FIXED PA AUTOMATION PIPELINE - NO MORE GUESSING!")
    print("=" * 100)
    
    results = {}
    
    # Run Akshay (should work)
    print("\n🔸 Running FIXED Akshay pipeline...")
    akshay_result = run_fixed_akshay()
    results["Akshay"] = akshay_result
    
    # Run Abdullah (may fail due to encryption)
    print("\n🔸 Running FIXED Abdullah pipeline...")
    abdullah_result = run_fixed_abdullah()
    results["Abdullah"] = abdullah_result
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 FIXED PIPELINE SUMMARY")
    print("=" * 100)
    
    for patient, result in results.items():
        if result:
            status = "✅ SUCCESS" if result.get('overall_success', False) else "❌ FAILED"
            extraction_rate = result.get('data_extraction', {}).get('extraction_rate', 'N/A')
            actual_fill_rate = result.get('form_filling', {}).get('actual_fill_rate', 'N/A')
            validation_rate = result.get('field_mapping', {}).get('validation_rate', 'N/A')
            
            print(f"{patient:>10}: {status}")
            print(f"{'':>10}  Extraction: {extraction_rate}")
            print(f"{'':>10}  Field Validation: {validation_rate}")
            print(f"{'':>10}  Actual Fill Rate: {actual_fill_rate}")
        else:
            print(f"{patient:>10}: ❌ FAILED (Missing files or encrypted PDF)")
        print()
    
    return results

if __name__ == "__main__":
    results = run_fixed_pipelines()