#!/usr/bin/env python3
"""
PDF Field Analyzer - Reads actual PDF field names and properties using pypdf
This replaces the guessing approach with programmatic field discovery.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

from pypdf import PdfReader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFFieldAnalyzer:
    """Analyzes PDF forms to extract actual field names and properties."""
    
    def read_actual_pdf_fields(self, pdf_path: Path) -> Dict[str, Any]:
        """
        Read actual field names and properties from PDF using pypdf.
        
        Args:
            pdf_path: Path to PDF form
            
        Returns:
            Dict with actual field information
        """
        
        logger.info(f"Reading actual PDF fields from: {pdf_path.name}")
        
        try:
            reader = PdfReader(pdf_path)
            
            if reader.is_encrypted:
                logger.error(f"PDF is encrypted: {pdf_path.name}")
                return {"error": "PDF is encrypted", "fields": {}}
            
            # Get form fields
            form_fields = {}
            total_fields = 0
            
            if reader.metadata and hasattr(reader, 'get_form_text_fields'):
                # Try to get form fields
                try:
                    text_fields = reader.get_form_text_fields()
                    for field_name, value in text_fields.items():
                        form_fields[field_name] = {
                            "field_type": "text",
                            "current_value": value,
                            "field_name": field_name
                        }
                        total_fields += 1
                except Exception as e:
                    logger.warning(f"Could not get text fields: {e}")
            
            # Alternative method: iterate through pages and annotations
            if not form_fields:
                logger.info("Trying alternative method to read form fields...")
                
                for page_num, page in enumerate(reader.pages):
                    if "/Annots" in page:
                        annotations = page["/Annots"]
                        
                        for annot_ref in annotations:
                            try:
                                annot = annot_ref.get_object()
                                
                                if "/Subtype" in annot and annot["/Subtype"] == "/Widget":
                                    # This is a form field
                                    field_name = None
                                    field_type = "unknown"
                                    
                                    # Get field name
                                    if "/T" in annot:
                                        field_name = str(annot["/T"])
                                    elif "/Parent" in annot:
                                        parent = annot["/Parent"].get_object()
                                        if "/T" in parent:
                                            field_name = str(parent["/T"])
                                    
                                    # Get field type
                                    if "/FT" in annot:
                                        ft = annot["/FT"]
                                        if ft == "/Tx":
                                            field_type = "text"
                                        elif ft == "/Btn":
                                            field_type = "button"
                                        elif ft == "/Ch":
                                            field_type = "choice"
                                    
                                    # Get current value
                                    current_value = None
                                    if "/V" in annot:
                                        current_value = str(annot["/V"])
                                    
                                    if field_name:
                                        form_fields[field_name] = {
                                            "field_type": field_type,
                                            "current_value": current_value,
                                            "field_name": field_name,
                                            "page": page_num + 1
                                        }
                                        total_fields += 1
                                        
                            except Exception as e:
                                logger.debug(f"Could not process annotation: {e}")
                                continue
            
            logger.info(f"Found {total_fields} actual PDF fields")
            
            return {
                "pdf_path": str(pdf_path),
                "total_fields": total_fields,
                "fields": form_fields,
                "analysis_success": True
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze PDF fields: {e}")
            return {
                "pdf_path": str(pdf_path),
                "error": str(e),
                "fields": {},
                "analysis_success": False
            }
    
    def create_field_mapping(self, actual_fields: Dict[str, Any], semantic_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Create mapping between extracted semantic data and actual PDF field names.
        
        Args:
            actual_fields: Real PDF field names from pypdf
            semantic_data: Extracted data with semantic keys
            
        Returns:
            Mapping dict: {semantic_key: actual_field_name}
        """
        
        logger.info("Creating field mapping between semantic data and actual PDF fields")
        
        field_mapping = {}
        pdf_fields = actual_fields.get("fields", {})
        
        # Common mappings based on typical PA form patterns
        mapping_patterns = {
            # Patient demographics
            "patient_first_name": ["First Name", "Patient First Name", "Indicate T.2", "T11", "first_name"],
            "patient_last_name": ["Last Name", "Patient Last Name", "Indicate T.3", "T12", "last_name"],
            "patient_dob": ["DOB", "Date of Birth", "Indicate T.4", "T13", "dob"],
            
            # Insurance info  
            "member_id": ["Member ID", "Insurance Member ID", "Indicate T.5", "T14", "member_id"],
            "group_number": ["Group Number", "Group #", "Insurance Group", "group_number"],
            
            # Clinical data
            "diagnosis": ["Diagnosis", "Primary Diagnosis", "ICD Code", "T15", "diagnosis"],
            "icd_code": ["ICD Code", "ICD-10", "Primary ICD", "diagnosis_code"],
            
            # Medication info
            "drug_name": ["Drug Name", "Medication", "Requested Drug", "T16", "medication"],
            "dosage": ["Dosage", "Dose", "Medication Dose", "T17", "dose"],
            
            # Provider info
            "prescriber_name": ["Prescriber", "Physician", "Request by T", "prescriber"],
            "prescriber_phone": ["Phone", "Prescriber Phone", "Phone T", "phone"],
            "prescriber_fax": ["Fax", "Prescriber Fax", "Fax T", "fax"],
        }
        
        # Try to map each semantic key to an actual PDF field
        for semantic_key, value in semantic_data.items():
            if value is None:
                continue
                
            best_match = None
            
            # Direct key match first
            if semantic_key in pdf_fields:
                best_match = semantic_key
            else:
                # Try pattern matching
                for pattern_key, possible_names in mapping_patterns.items():
                    if pattern_key in semantic_key.lower() or semantic_key in possible_names:
                        # Look for matching PDF field names
                        for possible_name in possible_names:
                            if possible_name in pdf_fields:
                                best_match = possible_name
                                break
                        if best_match:
                            break
                
                # Fuzzy matching as fallback
                if not best_match:
                    semantic_lower = semantic_key.lower()
                    for pdf_field_name in pdf_fields.keys():
                        pdf_lower = pdf_field_name.lower()
                        if (semantic_lower in pdf_lower or 
                            pdf_lower in semantic_lower or
                            any(word in pdf_lower for word in semantic_lower.split('_'))):
                            best_match = pdf_field_name
                            break
            
            if best_match:
                field_mapping[semantic_key] = best_match
                logger.info(f"Mapped: {semantic_key} -> {best_match}")
            else:
                logger.warning(f"Could not map semantic key: {semantic_key}")
        
        logger.info(f"Created {len(field_mapping)} field mappings")
        return field_mapping
    
    def validate_field_mapping(self, field_mapping: Dict[str, str], actual_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that mapped field names actually exist in the PDF.
        
        Args:
            field_mapping: Semantic to PDF field mapping
            actual_fields: Real PDF field information
            
        Returns:
            Validation results
        """
        
        logger.info("Validating field mapping against actual PDF fields")
        
        pdf_fields = actual_fields.get("fields", {})
        valid_mappings = {}
        invalid_mappings = {}
        
        for semantic_key, pdf_field_name in field_mapping.items():
            if pdf_field_name in pdf_fields:
                valid_mappings[semantic_key] = pdf_field_name
            else:
                invalid_mappings[semantic_key] = pdf_field_name
                logger.warning(f"Invalid mapping: {semantic_key} -> {pdf_field_name} (field does not exist)")
        
        validation_results = {
            "total_mappings": len(field_mapping),
            "valid_mappings": len(valid_mappings),
            "invalid_mappings": len(invalid_mappings),
            "validation_rate": f"{len(valid_mappings)/len(field_mapping)*100:.1f}%" if field_mapping else "0%",
            "valid_fields": valid_mappings,
            "invalid_fields": invalid_mappings
        }
        
        logger.info(f"Validation complete: {validation_results['validation_rate']} success rate")
        return validation_results
    
    def generate_field_analysis_report(self, pdf_path: Path, output_dir: Path) -> Path:
        """Generate comprehensive field analysis report."""
        
        # Analyze PDF fields
        field_analysis = self.read_actual_pdf_fields(pdf_path)
        
        # Save detailed report
        report_path = output_dir / f"{pdf_path.stem}_FIELD_ANALYSIS.json"
        with open(report_path, 'w') as f:
            json.dump(field_analysis, f, indent=2)
        
        # Create human-readable summary
        summary_path = output_dir / f"{pdf_path.stem}_FIELD_SUMMARY.md"
        with open(summary_path, 'w') as f:
            f.write(f"# PDF Field Analysis Report - {pdf_path.name}\n\n")
            
            if field_analysis.get("analysis_success", False):
                f.write(f"## Analysis Results\n\n")
                f.write(f"- **Total Fields Found**: {field_analysis['total_fields']}\n")
                f.write(f"- **Analysis Status**: ✅ SUCCESS\n\n")
                
                f.write(f"## Field Details\n\n")
                fields = field_analysis.get("fields", {})
                
                for field_name, field_info in fields.items():
                    f.write(f"### {field_name}\n")
                    f.write(f"- **Type**: {field_info.get('field_type', 'unknown')}\n")
                    f.write(f"- **Current Value**: {field_info.get('current_value', 'empty')}\n")
                    if 'page' in field_info:
                        f.write(f"- **Page**: {field_info['page']}\n")
                    f.write("\n")
            else:
                f.write(f"## Analysis Failed\n\n")
                f.write(f"- **Error**: {field_analysis.get('error', 'Unknown error')}\n")
                f.write(f"- **Analysis Status**: ❌ FAILED\n")
        
        logger.info(f"Field analysis report saved: {report_path}")
        logger.info(f"Field summary saved: {summary_path}")
        
        return report_path

def test_field_analyzer():
    """Test the PDF field analyzer on both forms."""
    
    print("=" * 80)
    print("🔍 TESTING PDF FIELD ANALYZER")
    print("=" * 80)
    
    analyzer = PDFFieldAnalyzer()
    output_dir = Path("expert_output")
    output_dir.mkdir(exist_ok=True)
    
    # Test on both PDF forms
    test_files = [
        Path("Input Data/Adbulla/PA.pdf"),
        Path("Input Data/Akshay/pa.pdf")
    ]
    
    for pdf_path in test_files:
        if pdf_path.exists():
            print(f"\n📋 Analyzing: {pdf_path}")
            
            # Generate field analysis
            report_path = analyzer.generate_field_analysis_report(pdf_path, output_dir)
            
            # Read and display results
            field_analysis = analyzer.read_actual_pdf_fields(pdf_path)
            
            if field_analysis.get("analysis_success", False):
                total_fields = field_analysis.get("total_fields", 0)
                print(f"✅ Found {total_fields} actual PDF fields")
                
                # Show first few field names
                fields = field_analysis.get("fields", {})
                if fields:
                    print(f"📝 Sample field names:")
                    for i, field_name in enumerate(list(fields.keys())[:5]):
                        print(f"   {i+1}. {field_name}")
                    if len(fields) > 5:
                        print(f"   ... and {len(fields)-5} more")
            else:
                print(f"❌ Analysis failed: {field_analysis.get('error', 'Unknown error')}")
        else:
            print(f"❌ File not found: {pdf_path}")
    
    print(f"\n📊 Analysis complete! Check {output_dir} for detailed reports.")

if __name__ == "__main__":
    test_field_analyzer()