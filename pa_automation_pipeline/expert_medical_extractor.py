#!/usr/bin/env python3
"""
Expert Medical Data-Extraction Agent for PA PDF AcroForms
Implements the complete system prompt requirements for production-grade medical extraction.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

import google.generativeai as genai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExpertMedicalExtractor:
    """Expert medical data-extraction agent filling PA PDF AcroForms."""
    
    def __init__(self):
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in .env file.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name="gemini-2.0-flash")
    
    def _upload_file(self, file_path: Path):
        """Upload file to <PERSON>."""
        logger.info(f"Uploading {file_path.name} for expert medical extraction...")
        uploaded_file = genai.upload_file(path=file_path)
        
        while uploaded_file.state.name == "PROCESSING":
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)
            
        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File upload failed: {uploaded_file.name}")
            
        return uploaded_file
    
    def extract_medical_data(self, referral_pdf_path: Path, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Expert medical data extraction from 30-50 page clinical PDFs.
        
        Args:
            referral_pdf_path: Path to scanned/OCR medical documents
            schema: Field schema with conditional logic and checkbox export values
            
        Returns:
            Dict with field_id: value|null mapping
        """
        
        try:
            referral_file = self._upload_file(referral_pdf_path)
            
            # Create expert medical extraction prompt
            extraction_prompt = self._create_expert_medical_prompt(schema)
            
            logger.info("Performing expert medical data extraction...")
            response = self.model.generate_content([extraction_prompt, referral_file])
            
            # Parse extraction response
            extracted_data = self._parse_medical_response(response.text)
            
            # Apply conditional logic and validation
            validated_data = self._apply_conditional_logic(extracted_data, schema)
            
            # Final medical validation
            final_data = self._medical_validation(validated_data, schema)
            
            extraction_count = sum(1 for v in final_data.values() if v is not None)
            total_fields = len(schema.get('fields', {}))
            
            logger.info(f"Expert medical extraction: {extraction_count}/{total_fields} fields")
            
            return {
                'extracted_data': final_data,
                'extraction_count': extraction_count,
                'total_fields': total_fields,
                'extraction_rate': f"{extraction_count/total_fields*100:.1f}%" if total_fields > 0 else "0%",
                'medical_validation': 'PASSED'
            }
            
        except Exception as e:
            logger.error(f"Expert medical extraction failed: {e}")
            return {'error': str(e)}
        finally:
            if 'referral_file' in locals():
                try:
                    genai.delete_file(referral_file.name)
                except:
                    pass
    
    def _create_expert_medical_prompt(self, schema: Dict[str, Any]) -> str:
        """Create expert medical extraction prompt following system requirements."""
        
        fields = schema.get('fields', {})
        conditions = schema.get('conditions', {})
        
        prompt = """### ROLE
Expert medical data-extraction agent filling PA PDF AcroForms.

### CONTEXT
• Inputs are scanned/OCR PDFs of 30-50 pages (clinical notes, labs, etc.).
• Output JSON: {field_id: value|null}. Field IDs come from provided schema.
• Always use null when unsure. Never hallucinate.

### KEY CHALLENGES
• Poor handwriting, fax artefacts, multi-column layouts.
• Conditional PA logic (mutually exclusive fields, drug-specific evidence).
• Complex dosing (mg/kg, BSA) and biosimilar naming.

### EXTRACTION RULES
1. Accuracy over completeness.
2. Use semantic clues + spatial context.
3. Respect conditional logic; fill only relevant sections.
4. For checkboxes, set export value from schema or `/Yes`.

## FIELD SCHEMA

"""
        
        # Add field definitions with conditional logic
        for field_id, field_info in fields.items():
            field_type = field_info.get('field_type', 'text')
            semantic_meaning = field_info.get('semantic_meaning', field_id)
            extraction_guidance = field_info.get('extraction_guidance', '')
            checkbox_export = field_info.get('checkbox_export_value', '/Yes')
            
            prompt += f"""
**{field_id}** ({field_type})
- Semantic: {semantic_meaning}
- Extract: {extraction_guidance}"""
            
            if field_type == 'checkbox':
                prompt += f"\n- Export value: {checkbox_export}"
            
            # Add conditional logic
            if field_id in conditions:
                condition = conditions[field_id]
                prompt += f"\n- Conditional: {condition}"
        
        prompt += """

## MEDICAL EXTRACTION STRATEGY

### DOCUMENT NAVIGATION (30-50 pages)
**Page 1-5: Demographics & Insurance**
- Patient registration, insurance cards, eligibility verification
- Look for: Member IDs, group numbers, policy details

**Page 6-15: Clinical History** 
- Progress notes, physician assessments, medical history
- Look for: Diagnosis codes (ICD-10), previous treatments, comorbidities

**Page 16-25: Laboratory & Diagnostics**
- Lab results, imaging reports, pathology
- Look for: Test values, biomarkers, diagnostic criteria

**Page 26-35: Treatment Plans**
- Prescription orders, treatment protocols, dosing schedules
- Look for: Drug names, dosages (mg/kg, BSA), administration routes

**Page 36-50: Supporting Documentation**
- Prior authorizations, appeals, pharmacy records
- Look for: Previous approvals, formulary status, step therapy

### EXTRACTION TECHNIQUES

**Poor Handwriting/Fax Artifacts:**
- Use context clues from printed headers/labels
- Cross-reference multiple mentions
- Prioritize typed/printed text over handwritten

**Multi-column Layouts:**
- Read left-to-right, top-to-bottom within each column
- Identify form sections by headers/borders
- Maintain spatial relationships

**Complex Dosing:**
- Capture full dosing string: "10 mg/kg IV q3weeks"
- Include BSA calculations: "375 mg/m² subcutaneous"
- Note biosimilar names: "adalimumab-atto (Amjevita)"

**Conditional Logic:**
- Medicare vs Commercial insurance → different evidence requirements
- Drug-specific fields → only fill for relevant medication
- Mutually exclusive options → choose most appropriate

## OUTPUT REQUIREMENTS

Return ONLY valid JSON with exact field IDs:

```json
{"""
        
        # Show field examples
        example_fields = list(fields.keys())[:3]
        for i, field_id in enumerate(example_fields):
            field_info = fields[field_id]
            field_type = field_info.get('field_type', 'text')
            
            if field_type == 'checkbox':
                export_value = field_info.get('checkbox_export_value', '/Yes')
                example_value = f'"{export_value}"'
            else:
                example_value = '"extracted_value_or_null"'
            
            comma = "," if i < len(example_fields) - 1 else ""
            prompt += f'\n  "{field_id}": {example_value}{comma}'
        
        prompt += """
}
```

### CRITICAL MEDICAL REQUIREMENTS
1. **Never hallucinate** - Use null when uncertain
2. **Medical accuracy** - Verify drug names, dosages, ICD codes
3. **Conditional compliance** - Respect mutual exclusions
4. **Spatial awareness** - Use document layout context
5. **Evidence-based** - Only extract what's clearly documented
6. **FIELD ID MAPPING** - Use EXACT field IDs from schema, NOT semantic meanings

Extract medical data with expert precision and clinical accuracy using the EXACT field IDs provided."""
        
        return prompt
    
    def _parse_medical_response(self, response_text: str) -> Dict[str, Any]:
        """Parse JSON response with medical data validation."""
        
        try:
            # Try to find JSON in code blocks first
            import re
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # Try direct JSON parsing
            start = response_text.find('{')
            end = response_text.rfind('}') + 1
            if start != -1 and end > start:
                return json.loads(response_text[start:end])
            
            logger.error("Could not parse medical extraction JSON")
            return {}
            
        except json.JSONDecodeError as e:
            logger.error(f"Medical JSON parsing failed: {e}")
            return {}
    
    def _apply_conditional_logic(self, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Apply conditional logic and mutual exclusions."""
        
        conditions = schema.get('conditions', {})
        validated_data = extracted_data.copy()
        
        for field_id, condition in conditions.items():
            if 'enabled_if' in condition:
                # Check if enabling condition is met
                enable_condition = condition['enabled_if']
                condition_met = True
                
                for condition_field, required_value in enable_condition.items():
                    if extracted_data.get(condition_field) != required_value:
                        condition_met = False
                        break
                
                # If condition not met, null out the field
                if not condition_met:
                    validated_data[field_id] = None
                    logger.info(f"Conditional logic: nulled {field_id} (condition not met)")
            
            if 'mutually_exclusive' in condition:
                # Handle mutually exclusive fields
                exclusive_group = condition['mutually_exclusive']
                filled_fields = [f for f in exclusive_group if extracted_data.get(f) is not None]
                
                if len(filled_fields) > 1:
                    # Keep only the first filled field, null others
                    for field in filled_fields[1:]:
                        validated_data[field] = None
                        logger.info(f"Mutual exclusion: nulled {field}")
        
        return validated_data
    
    def _medical_validation(self, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, str]:
        """Final medical validation and formatting."""
        
        import re  # Import re at the function level
        fields = schema.get('fields', {})
        validated = {}
        
        for field_id in fields.keys():
            value = extracted_data.get(field_id)
            field_info = fields[field_id]
            field_type = field_info.get('field_type', 'text')
            
            if value is None:
                validated[field_id] = None
                continue
            
            # Medical validation by field type
            if field_type == 'checkbox':
                # Use proper export value
                export_value = field_info.get('checkbox_export_value', '/Yes')
                validated[field_id] = export_value
            
            elif field_type == 'text':
                # Clean and validate medical text
                cleaned_value = str(value).strip()
                
                # Medical-specific validation
                if 'icd' in field_info.get('semantic_meaning', '').lower():
                    # Validate ICD-10 format
                    if re.match(r'^[A-TV-Z]\d{2,3}(\.\d{1,4})?$', cleaned_value.upper()):
                        validated[field_id] = cleaned_value.upper()
                    else:
                        validated[field_id] = None
                        logger.warning(f"Invalid ICD-10 format: {cleaned_value}")
                
                elif 'npi' in field_info.get('semantic_meaning', '').lower():
                    # Validate NPI format (10 digits)
                    digits = re.sub(r'\D', '', cleaned_value)
                    if len(digits) == 10:
                        validated[field_id] = digits
                    else:
                        validated[field_id] = None
                        logger.warning(f"Invalid NPI format: {cleaned_value}")
                
                else:
                    validated[field_id] = cleaned_value
            
            else:
                validated[field_id] = str(value).strip()
        
        return validated

def test_expert_extractor():
    """Test the expert medical extractor."""
    
    print("=" * 80)
    print("🏥 TESTING EXPERT MEDICAL EXTRACTOR")
    print("=" * 80)
    
    # Create test schema with conditions
    test_schema = {
        'fields': {
            'T11': {
                'semantic_meaning': 'patient_first_name',
                'field_type': 'text',
                'extraction_guidance': 'Patient first name from demographics section'
            },
            'T12': {
                'semantic_meaning': 'patient_last_name', 
                'field_type': 'text',
                'extraction_guidance': 'Patient last name from demographics section'
            },
            'CB_Medicare': {
                'semantic_meaning': 'medicare_plan',
                'field_type': 'checkbox',
                'checkbox_export_value': '/1',
                'extraction_guidance': 'Check if patient has Medicare coverage'
            },
            'T_Medicare_ID': {
                'semantic_meaning': 'medicare_member_id',
                'field_type': 'text',
                'extraction_guidance': 'Medicare member identification number'
            }
        },
        'conditions': {
            'T_Medicare_ID': {
                'enabled_if': {'CB_Medicare': '/1'}
            }
        }
    }
    
    extractor = ExpertMedicalExtractor()
    referral_path = Path("Input Data/Akshay/referral_package.pdf")
    
    if not referral_path.exists():
        print("❌ Referral package not found!")
        return
    
    # Test expert extraction
    result = extractor.extract_medical_data(referral_path, test_schema)
    
    if 'error' not in result:
        print(f"\n🏥 EXPERT MEDICAL EXTRACTION RESULTS:")
        print(f"✅ Extraction rate: {result['extraction_rate']}")
        print(f"📝 Fields extracted: {result['extraction_count']}/{result['total_fields']}")
        print(f"🔬 Medical validation: {result['medical_validation']}")
        
        extracted_data = result['extracted_data']
        
        print(f"\n📊 EXTRACTED MEDICAL DATA:")
        print("-" * 50)
        for field_id, value in extracted_data.items():
            if value is not None:
                print(f"  {field_id}: {value}")
            else:
                print(f"  {field_id}: null")
        
        return result
    else:
        print(f"❌ Expert extraction failed: {result['error']}")
        return None

if __name__ == "__main__":
    result = test_expert_extractor()
    
    if result:
        print(f"\n✅ Expert medical extraction successful!")
    else:
        print(f"\n❌ Expert medical extraction failed!")