{"extracted_data": {"T11": {"value": "<PERSON><PERSON><PERSON>", "page_found": 1, "confidence": "High", "thought_process": "I will check patient registration forms, insurance cards, and demographic sections, typically pages 1-3, for the patient's first name.", "search_result": "Found patient name on page 1: \"<PERSON><PERSON><PERSON>\".", "justification": "The first name listed on the Skyrizi Order Form is <PERSON><PERSON><PERSON>."}, "T12": {"value": "chaud<PERSON>", "page_found": 1, "confidence": "High", "thought_process": "I will check patient registration forms, insurance cards, and demographic sections, typically pages 1-3, for the patient's last name.", "search_result": "Found patient name on page 1: \"<PERSON><PERSON><PERSON>\".", "justification": "The last name listed on the Skyrizi Order Form is chaud<PERSON>."}, "T13": {"value": "02/17/1987", "page_found": 1, "confidence": "High", "thought_process": "I will look for 'Date of Birth', 'DOB', or birth date fields in patient demographics on pages 1-3.", "search_result": "Found 'Date of Birth: 02/17/1987' on page 1 in the Skyrizi Order Form.", "justification": "The date of birth is clearly listed as 02/17/1987 on the Skyrizi Order Form."}, "T14": {"value": "350410732018", "page_found": 10, "confidence": "High", "thought_process": "Scan insurance cards and verification forms for 'Member ID', 'Subscriber ID', or policy numbers, likely on pages 1-3 or near insurance information.", "search_result": "Found 'Policy #: 350410732018' on page 10 under Patient Insurance Information.", "justification": "The policy number listed is 350410732018, which can be considered the member ID."}, "T15": {"value": "<PERSON><PERSON><PERSON>'s disease", "page_found": 1, "confidence": "High", "thought_process": "Check clinical notes, assessment sections, ICD-10 codes, and diagnosis lists on all pages, but especially 1-3.", "search_result": "Page 1 indicates '<PERSON><PERSON><PERSON>'s Disease Induction Phase' and '<PERSON><PERSON><PERSON>'s Disease Maintenance Phase'. Page 3 also mentions '<PERSON><PERSON><PERSON>'s disease of colon with rectal bleeding - K50.111'", "justification": "The patient is being treated for <PERSON><PERSON><PERSON>'s disease."}, "T16": {"value": "Skyrizi", "page_found": 1, "confidence": "High", "thought_process": "Look in treatment plans, prescription orders, and medication requests, which are likely on page 1.", "search_result": "The document is a 'SKYRIZI ORDER FORM' on page 1, indicating <PERSON><PERSON><PERSON> is the requested medication.", "justification": "The form is specifically for ordering Skyrizi."}, "T17": {"value": "600mg IV at week 0, week 4 and week 8 per protocol; 180mg SQ at week 12 and every 8 weeks thereafter; 360mg SQ at week 12 and every 8 weeks thereafter", "page_found": 1, "confidence": "High", "thought_process": "Dosage is found near medication names in treatment protocols and prescription details, typically on page 1.", "search_result": "Page 1 details the dosage of Skyrizi: \"Administer Skyrizi 600mg IV at week 0, week 4 and week 8 per protocol.  180mg SQ at week 12 and every 8 weeks thereafter. X360mg SQ at week 12 and every 8 weeks thereafter.\"", "justification": "The dosage is explicitly stated in the Skyrizi Order Form."}, "T18": {"value": null, "page_found": null, "confidence": "Low", "thought_process": "Located on insurance cards, often next to member ID, which may be pages 1-3 or the insurance card found on page 9.", "search_result": "The insurance card on page 9 is too blurry to read the Group Number. Page 10 does not contain group number.", "justification": "The insurance card is illegible."}, "T20": {"value": "************", "page_found": 2, "confidence": "High", "thought_process": "Scan contact information in patient registration and demographic forms, typically on pages 1-3.", "search_result": "Patient phone number 'Home: ************' is listed on page 2.", "justification": "Patient contact information includes the phone number."}, "T21": {"value": "1460 El Camino Real, Arlington, VA-22407", "page_found": 2, "confidence": "High", "thought_process": "Found in patient registration, contact forms, and insurance verification, usually on pages 1-3.", "search_result": "Patient address '1460 El Camino Real, Arlington, VA-22407' is on page 2.", "justification": "Patient contact information includes the address."}, "Request by T": {"value": "<PERSON>", "page_found": 1, "confidence": "High", "thought_process": "Check letterheads, signatures, and provider information sections on all pages, especially the first page.", "search_result": "Prescriber name '<PERSON>, <PERSON>' is listed on the Skyrizi order form on page 1.", "justification": "The prescriber is clearly identified on the Skyrizi order form."}, "Phone T": {"value": "************", "page_found": 1, "confidence": "High", "thought_process": "Look in provider contact information, letterheads, and signature blocks, often on the first page.", "search_result": "The Provider Phone number '************' is listed on page 1.", "justification": "Prescriber phone number is easily found on the Skyrizi Order form"}, "Fax T": {"value": "************", "page_found": 1, "confidence": "High", "thought_process": "Same locations as prescriber phone, often listed together, usually on the first page.", "search_result": "Provider Fax number '************' is listed on page 1.", "justification": "Prescriber fax number is easily found on the Skyrizi order form"}}, "summary": {"total_fields": 13, "successful_extractions": 12, "null_extractions": 1, "extraction_rate": "12/13", "extraction_percentage": "92.3%", "confidence_breakdown": {"high_confidence": ["T11", "T12", "T13", "T14", "T15", "T16", "T17", "T20", "T21", "Request by T", "Phone T", "Fax T"], "medium_confidence": [], "low_confidence": [], "null_with_reasoning": [{"field": "T18", "reason": "The insurance card is illegible."}]}, "high_confidence_count": 12, "medium_confidence_count": 0, "low_confidence_count": 0}, "extraction_type": "enhanced_reasoning"}