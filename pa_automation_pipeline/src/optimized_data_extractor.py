import json
import logging
import os
import re
import time
from pathlib import Path
from typing import Dict, Any, List, Tuple

import google.generativeai as genai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FieldRanker:
    """Ranks form fields by importance and extraction likelihood"""
    
    def __init__(self):
        # High-priority fields that are commonly available in referrals
        self.high_priority_fields = {
            'patient_first_name': 10,
            'patient_last_name': 10,
            'patient_full_name': 9,
            'patient_dob': 9,
            'patient_address': 8,
            'patient_phone': 8,
            'insurance_member_id': 10,
            'provider_npi': 9,
            'provider_name': 8,
            'patient_city': 7,
            'patient_state': 7,
            'patient_zip': 7,
            'insurance_group': 6,
            'provider_phone': 5,
            'provider_fax': 4
        }

    def rank_schema_fields(self, schema: Dict) -> List[Tuple[str, Dict, int]]:
        """Rank schema fields by extraction priority"""
        ranked_fields = []
        
        for field_name, field_info in schema.get('fields', {}).items():
            # Map field name to canonical key for priority lookup
            canonical_key = self._map_to_canonical(field_name)
            priority = self.high_priority_fields.get(canonical_key, 1)
            ranked_fields.append((field_name, field_info, priority))
        
        # Sort by priority (highest first)
        ranked_fields.sort(key=lambda x: x[2], reverse=True)
        
        logger.info(f"🎯 Ranked {len(ranked_fields)} schema fields by priority")
        return ranked_fields

    def get_priority_subset(self, ranked_fields: List[Tuple[str, Dict, int]], max_fields: int = 15) -> List[Tuple[str, Dict, int]]:
        """Get high-priority subset of fields"""
        priority_fields = [f for f in ranked_fields if f[2] >= 6][:max_fields]
        logger.info(f"📋 Selected {len(priority_fields)} high-priority fields for extraction")
        return priority_fields

    def _map_to_canonical(self, field_name: str) -> str:
        """Map field name to canonical key"""
        field_lower = field_name.lower()
        
        # Direct mappings
        if 'first' in field_lower and 'name' in field_lower:
            return 'patient_first_name'
        elif 'last' in field_lower and 'name' in field_lower:
            return 'patient_last_name'
        elif 'full' in field_lower and 'name' in field_lower:
            return 'patient_full_name'
        elif 'dob' in field_lower or 'birth' in field_lower:
            return 'patient_dob'
        elif 'address' in field_lower:
            return 'patient_address'
        elif 'phone' in field_lower:
            return 'patient_phone'
        elif 'member' in field_lower or 'insurance' in field_lower:
            return 'insurance_member_id'
        elif 'npi' in field_lower:
            return 'provider_npi'
        elif 'provider' in field_lower or 'physician' in field_lower:
            return 'provider_name'
        elif 'city' in field_lower:
            return 'patient_city'
        elif 'state' in field_lower:
            return 'patient_state'
        elif 'zip' in field_lower:
            return 'patient_zip'
        
        return field_name.lower()

class OptimizedDataExtractor:
    """Optimized data extractor with field ranking and two-phase extraction"""
    
    def __init__(self):
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in .env file.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp")
        self.ranker = FieldRanker()

    def _upload_file(self, file_path: Path) -> Any:
        """Uploads a file to the Gemini API and waits for it to be processed."""
        logger.info(f"📤 Uploading file to Gemini: {file_path.name}")
        uploaded_file = genai.upload_file(path=file_path)
        
        while uploaded_file.state.name == "PROCESSING":
            logger.info("⏳ File is processing...")
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)

        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File processing failed: {uploaded_file.name}")
        
        logger.info(f"✅ File uploaded and processed successfully: {uploaded_file.name}")
        return uploaded_file

    def _extract_json_from_response(self, text: str) -> Dict[str, Any]:
        """Safely extracts a JSON object from a string."""
        match = re.search(r"```json\s*(\{.*?\})\s*```", text, re.DOTALL)
        if match:
            json_str = match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON from response: {e}")
                raise ValueError("LLM response did not contain valid JSON.")
        
        # Fallback for plain JSON response
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            raise ValueError("Could not find a valid JSON object in the LLM response.")

    def phase1_classify_sections(self, referral_file: Any) -> Dict[str, str]:
        """Phase 1: Classify document sections"""
        
        prompt = """
        Analyze this medical referral document and identify the main sections.
        
        Return a JSON object with these section classifications:
        {
            "patient_demographics": "Brief description of where patient info is located",
            "insurance_information": "Brief description of where insurance info is located", 
            "provider_information": "Brief description of where provider info is located",
            "medical_history": "Brief description of where medical history is located",
            "diagnosis_treatment": "Brief description of where diagnosis/treatment info is located"
        }
        
        Focus on identifying WHERE each type of information appears in the document.
        """
        
        try:
            logger.info("🔍 Phase 1: Classifying document sections...")
            response = self.model.generate_content([prompt, referral_file])
            sections = self._extract_json_from_response(response.text)
            logger.info(f"📑 Classified {len(sections)} document sections")
            return sections
        except Exception as e:
            logger.error(f"❌ Phase 1 classification failed: {e}")
            return {}

    def phase2_targeted_extraction(self, referral_file: Any, priority_fields: List[Tuple[str, Dict, int]], sections: Dict[str, str]) -> Dict[str, Any]:
        """Phase 2: Target specific high-priority fields"""
        
        # Group fields by section for focused extraction
        field_groups = {
            'patient_demographics': [],
            'insurance_information': [],
            'provider_information': []
        }
        
        for field_name, field_info, priority in priority_fields:
            canonical_key = self.ranker._map_to_canonical(field_name)
            if 'patient' in canonical_key:
                field_groups['patient_demographics'].append((field_name, field_info, canonical_key))
            elif 'insurance' in canonical_key:
                field_groups['insurance_information'].append((field_name, field_info, canonical_key))
            elif 'provider' in canonical_key:
                field_groups['provider_information'].append((field_name, field_info, canonical_key))
        
        extracted_data = {}
        
        # Extract each group separately for better focus
        for section_name, fields in field_groups.items():
            if not fields:
                continue
                
            section_context = sections.get(section_name, "")
            group_data = self._extract_field_group(referral_file, fields, section_name, section_context)
            extracted_data.update(group_data)
        
        return extracted_data

    def _extract_field_group(self, referral_file: Any, fields: List[Tuple[str, Dict, str]], section_name: str, section_context: str) -> Dict[str, Any]:
        """Extract a specific group of related fields"""
        
        field_descriptions = []
        for field_name, field_info, canonical_key in fields:
            description = field_info.get('description', f'Extract the {field_name}')
            field_descriptions.append(f'"{field_name}": "{description}"')
        
        prompt = f"""
        Focus on the {section_name} section of this medical referral document.
        
        Section context: {section_context}
        
        Extract ONLY these specific fields and return as JSON:
        {{
            {', '.join(field_descriptions)}
        }}
        
        CRITICAL INSTRUCTIONS:
        1. Focus ONLY on the {section_name} section
        2. Extract exact values as they appear in the document
        3. Use "NOT_FOUND" if a field is not available
        4. For dates, use YYYY-MM-DD format
        5. For names, extract full names as written
        6. For addresses, include full address with city, state, zip
        
        Examples:
        - patient_first_name: "John" (not "John Doe")
        - patient_dob: "1985-03-15" (not "March 15, 1985")
        - insurance_member_id: "ABC123456789" (exact ID as shown)
        """
        
        try:
            logger.info(f"🎯 Extracting {len(fields)} fields from {section_name}...")
            response = self.model.generate_content([prompt, referral_file])
            group_data = self._extract_json_from_response(response.text)
            
            # Filter out NOT_FOUND values
            filtered_data = {k: v for k, v in group_data.items() if v != "NOT_FOUND" and v and str(v).strip()}
            
            logger.info(f"📋 Extracted {len(filtered_data)}/{len(fields)} fields from {section_name}")
            return filtered_data
            
        except Exception as e:
            logger.error(f"❌ Field group extraction failed for {section_name}: {e}")
            return {}

    def extract_data_from_referral_optimized(self, referral_pdf_path: Path, schema: Dict) -> Dict[str, Any]:
        """
        Optimized data extraction with field ranking and two-phase approach
        """
        if not referral_pdf_path.exists():
            raise FileNotFoundError(f"Referral package not found at {referral_pdf_path}")

        logger.info(f"🚀 OPTIMIZED EXTRACTION: {referral_pdf_path.name}")
        
        try:
            # Step 1: Rank schema fields by priority
            ranked_fields = self.ranker.rank_schema_fields(schema)
            priority_fields = self.ranker.get_priority_subset(ranked_fields, max_fields=15)
            
            # Step 2: Upload referral file
            referral_file = self._upload_file(referral_pdf_path)
            
            # Step 3: Two-phase extraction
            logger.info("🔍 Starting two-phase extraction...")
            
            # Phase 1: Classify document sections
            sections = self.phase1_classify_sections(referral_file)
            
            # Phase 2: Targeted field extraction
            extracted_data = self.phase2_targeted_extraction(referral_file, priority_fields, sections)
            
            logger.info(f"📊 Optimized extraction complete: {len(extracted_data)} fields extracted")
            
            return {
                'extracted_data': extracted_data,
                'extraction_metadata': {
                    'total_schema_fields': len(schema.get('fields', {})),
                    'priority_fields_selected': len(priority_fields),
                    'fields_extracted': len(extracted_data),
                    'extraction_rate': f"{len(extracted_data)}/{len(priority_fields)} ({len(extracted_data)/len(priority_fields)*100:.1f}%)" if priority_fields else "0%",
                    'sections_classified': list(sections.keys()) if sections else []
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Optimized extraction failed: {e}")
            return {'extracted_data': {}, 'extraction_metadata': {'error': str(e)}}
        finally:
            # Clean up the uploaded file
            try:
                genai.delete_file(referral_file.name)
                logger.info(f"🧹 Cleaned up uploaded file: {referral_file.name}")
            except:
                pass

    def extract_data_from_referral(self, referral_pdf_path: Path, schema: Dict) -> Dict[str, Any]:
        """
        Backward compatible method - uses optimized extraction by default
        """
        result = self.extract_data_from_referral_optimized(referral_pdf_path, schema)
        return result.get('extracted_data', {})