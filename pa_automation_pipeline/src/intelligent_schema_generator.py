"""
Intelligent Schema Generator - Uses <PERSON> to understand PA form fields through visual analysis.
This solves the core problem: knowing what T11, T12, etc. actually mean.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any

import google.generativeai as genai
from dotenv import load_dotenv
import yaml

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentSchemaGenerator:
    """Uses Gemini Vision to understand PA form field meanings through visual analysis."""
    
    def __init__(self):
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in .env file.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp")
    
    def _upload_file(self, file_path: Path):
        """Upload file to Gemini."""
        logger.info(f"Uploading {file_path.name} for visual analysis...")
        uploaded_file = genai.upload_file(path=file_path)
        
        while uploaded_file.state.name == "PROCESSING":
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)
            
        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File upload failed: {uploaded_file.name}")
            
        return uploaded_file
    
    def analyze_pa_form_visually(self, pa_form_path: Path) -> Dict[str, Any]:
        """
        Use Gemini Vision to analyze the PA form and understand what each field means.
        This creates an intelligent schema by reading the actual form layout.
        """
        
        try:
            pa_form_file = self._upload_file(pa_form_path)
            
            # Chain of Thought prompt for understanding PA form fields
            cot_prompt = """# Chain of Thought: PA Form Field Analysis

You are analyzing a Prior Authorization (PA) form to understand what each field is asking for. This is critical for accurate data extraction and form filling.

## **STEP 1: VISUAL FORM ANALYSIS**

Look at this PA form carefully and identify ALL the fillable fields. For each field, I need you to:

1. **Identify the field ID** (like T11, T12, Request by T, etc.)
2. **Read the field label** (the text next to or above the field)
3. **Understand the context** (what section of the form is this in?)
4. **Determine the purpose** (what type of information goes here?)

## **STEP 2: CHAIN OF THOUGHT REASONING**

For each field, think through:
- **Location**: Where on the form is this field?
- **Label**: What does the label or nearby text say?
- **Context**: Is this in a patient section, provider section, clinical section?
- **Type**: Is this asking for a name, date, ID number, diagnosis, medication?
- **Examples**: What would a typical value look like?

## **STEP 3: SEMANTIC CLASSIFICATION**

Classify each field into these categories:
- **patient_demographics**: Name, DOB, address, phone, etc.
- **insurance_info**: Member ID, group number, insurance details
- **clinical_data**: Diagnosis, ICD codes, symptoms, medical history
- **medication_info**: Drug name, dosage, administration, duration
- **provider_info**: Prescriber name, NPI, phone, fax, practice details
- **authorization_info**: PA numbers, approval dates, special requests

## **STEP 4: GENERATE INTELLIGENT SCHEMA**

Create a JSON schema that maps each field ID to its meaning:

```json
{
  "field_id": {
    "semantic_meaning": "what_this_field_represents",
    "category": "field_category",
    "label_text": "actual_label_on_form", 
    "field_type": "text|checkbox|dropdown",
    "extraction_guidance": "how_to_find_this_in_referral",
    "examples": ["example_value_1", "example_value_2"],
    "required": true/false
  }
}
```

## **EXTRACTION EXAMPLES BY FIELD TYPE**

### **Patient Demographics**
- First/Last Name: Look for patient identification sections
- DOB: Look for birth date in patient demographics
- Address: Patient contact information
- Phone: Patient phone numbers

### **Insurance Information**  
- Member ID: Insurance card or verification forms
- Group Number: Insurance group or employer info
- Insurance Name: Carrier information

### **Clinical Data**
- Diagnosis: Primary/secondary diagnoses with ICD-10 codes
- Symptoms: Clinical presentation and history
- Lab Results: Recent test values and dates

### **Medication Information**
- Drug Name: Specific medication being requested
- Dosage: Strength and administration schedule
- Duration: Treatment length and refills

### **Provider Information**
- Prescriber: Ordering physician name and credentials
- NPI: National Provider Identifier (10 digits)
- Practice: Clinic name and contact information

## **YOUR TASK**

Analyze this PA form image and create a comprehensive schema that accurately maps each field ID to its semantic meaning. Be thorough and precise - this schema will be used to extract data from referral packages and fill the form correctly.

Pay special attention to:
1. **Field positioning and context clues**
2. **Label text and instructions**
3. **Required vs optional fields**
4. **Logical groupings of related fields**

Return your analysis as a well-structured JSON schema."""

            logger.info("Analyzing PA form visually with Chain of Thought...")
            response = self.model.generate_content([cot_prompt, pa_form_file])
            
            # Parse the schema from response with robust JSON extraction
            import re
            
            logger.info("Parsing schema from Gemini response...")
            
            # Try multiple JSON extraction methods
            schema = None
            
            # Method 1: Look for JSON code blocks
            json_matches = re.findall(r"```json\s*(\{.*?\})\s*```", response.text, re.DOTALL)
            for match in json_matches:
                try:
                    schema = json.loads(match)
                    logger.info("Successfully parsed JSON from code block")
                    break
                except json.JSONDecodeError:
                    continue
            
            # Method 2: Find JSON objects in response
            if schema is None:
                json_objects = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', response.text)
                for obj in json_objects:
                    try:
                        candidate = json.loads(obj)
                        # Validate it looks like a schema with proper structure
                        if (isinstance(candidate, dict) and len(candidate) > 3 and 
                            self._validate_schema_structure(candidate)):
                            schema = candidate
                            logger.info("Successfully parsed JSON object from response")
                            break
                    except json.JSONDecodeError:
                        continue
            
            # Method 3: Manual JSON extraction and cleaning
            if schema is None:
                try:
                    # Find the largest JSON-like structure
                    start = response.text.find('{')
                    if start != -1:
                        # Count braces to find the end
                        brace_count = 0
                        end = start
                        for i, char in enumerate(response.text[start:]):
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0:
                                    end = start + i + 1
                                    break
                        
                        json_str = response.text[start:end]
                        
                        # Clean common JSON issues
                        json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas
                        json_str = re.sub(r',\s*]', ']', json_str)  # Remove trailing commas in arrays
                        
                        schema = json.loads(json_str)
                        logger.info("Successfully parsed JSON with cleaning")
                        
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"All JSON parsing methods failed: {e}")
                    logger.error(f"Response preview: {response.text[:500]}...")
                    
                    # Fallback: Create a minimal schema based on the response
                    schema = self._create_fallback_schema(response.text)
            
            if schema is None:
                logger.error("Could not extract any valid schema")
                return {}
            
            logger.info(f"Generated intelligent schema with {len(schema)} fields")
            return schema
            
        except Exception as e:
            logger.error(f"Schema generation failed: {e}")
            return {}
        finally:
            if 'pa_form_file' in locals():
                try:
                    genai.delete_file(pa_form_file.name)
                except:
                    pass
    
    def generate_targeted_extraction_prompt(self, schema: Dict[str, Any]) -> str:
        """Generate a targeted extraction prompt based on the intelligent schema."""
        
        prompt = """# Targeted PA Data Extraction

You are extracting data from a medical referral package to fill a Prior Authorization form. You now have precise knowledge of what each form field requires.

## **FIELD-BY-FIELD EXTRACTION GUIDE**

Based on visual analysis of the PA form, here's what each field needs:

"""
        
        # Add field-by-field guidance
        for field_id, field_info in schema.items():
            semantic_meaning = field_info.get('semantic_meaning', field_id)
            category = field_info.get('category', 'unknown')
            extraction_guidance = field_info.get('extraction_guidance', 'Look for this information in the referral')
            examples = field_info.get('examples', [])
            required = field_info.get('required', False)
            
            status = "🔴 REQUIRED" if required else "🟡 OPTIONAL"
            
            prompt += f"""
### **{field_id}** - {semantic_meaning} ({category}) {status}
- **What to extract**: {extraction_guidance}
- **Examples**: {', '.join(examples) if examples else 'N/A'}
"""
        
        prompt += f"""

## **EXTRACTION INSTRUCTIONS**

1. **Scan the entire referral package** for the information above
2. **Match data precisely** to field requirements
3. **Return null** if information is not found
4. **Use exact values** from the document

## **OUTPUT FORMAT**

Return a JSON object with ONLY the field IDs as keys:

```json
{{
"""
        
        # Add JSON template
        for field_id in schema.keys():
            prompt += f'  "{field_id}": "extracted_value_or_null",\n'
        
        prompt += """}}
```

**CRITICAL**: Use the exact field IDs shown above. Extract only what you can find with confidence."""
        
        return prompt
    
    def save_intelligent_schema(self, schema: Dict[str, Any], output_path: Path):
        """Save the intelligent schema with metadata."""
        
        enhanced_schema = {
            "schema_version": "intelligent_v1.0",
            "generated_by": "Gemini Visual Analysis",
            "total_fields": len(schema),
            "field_categories": {},
            "fields": schema
        }
        
        # Count categories
        for field_info in schema.values():
            if isinstance(field_info, dict):
                category = field_info.get('category', 'unknown')
            else:
                category = 'unknown'
            enhanced_schema["field_categories"][category] = enhanced_schema["field_categories"].get(category, 0) + 1
        
        with open(output_path, 'w') as f:
            yaml.dump(enhanced_schema, f, indent=2, default_flow_style=False)
        
        logger.info(f"Intelligent schema saved to {output_path}")
        return enhanced_schema
    
    def _create_fallback_schema(self, response_text: str) -> Dict[str, Any]:
        """Create a fallback schema if JSON parsing fails."""
        
        logger.info("Creating fallback schema from response analysis...")
        
        # Common PA form fields based on typical forms (using correct case)
        fallback_schema = {
            "T11": {
                "semantic_meaning": "patient_first_name",
                "category": "patient_demographics", 
                "field_type": "text",
                "extraction_guidance": "Patient's first name from demographics",
                "required": True,
                "examples": ["John", "Mary"]
            },
            "T12": {
                "semantic_meaning": "patient_last_name",
                "category": "patient_demographics",
                "field_type": "text", 
                "extraction_guidance": "Patient's last name from demographics",
                "required": True,
                "examples": ["Smith", "Johnson"]
            },
            "T13": {
                "semantic_meaning": "patient_dob",
                "category": "patient_demographics",
                "field_type": "text",
                "extraction_guidance": "Patient's date of birth in MM/DD/YYYY format",
                "required": True,
                "examples": ["01/15/1980", "03/22/1975"]
            },
            "T14": {
                "semantic_meaning": "member_id",
                "category": "insurance_info",
                "field_type": "text",
                "extraction_guidance": "Insurance member/subscriber ID",
                "required": True,
                "examples": ["123456789", "ABC123XYZ"]
            },
            "T15": {
                "semantic_meaning": "diagnosis",
                "category": "clinical_data",
                "field_type": "text",
                "extraction_guidance": "Primary diagnosis with ICD-10 code",
                "required": True,
                "examples": ["K50.111", "M05.79"]
            },
            "T16": {
                "semantic_meaning": "drug_name",
                "category": "medication_info",
                "field_type": "text",
                "extraction_guidance": "Requested medication name",
                "required": True,
                "examples": ["Skyrizi", "Humira"]
            },
            "T17": {
                "semantic_meaning": "dosage",
                "category": "medication_info",
                "field_type": "text",
                "extraction_guidance": "Medication dosage and administration",
                "required": False,
                "examples": ["40mg every other week", "150mg every 8 weeks"]
            },
            "Request by T": {
                "semantic_meaning": "prescriber_name",
                "category": "provider_info",
                "field_type": "text",
                "extraction_guidance": "Prescribing physician name",
                "required": True,
                "examples": ["Dr. John Smith", "Jane Doe, MD"]
            },
            "Phone T": {
                "semantic_meaning": "prescriber_phone",
                "category": "provider_info",
                "field_type": "text",
                "extraction_guidance": "Prescriber phone number",
                "required": False,
                "examples": ["(*************", "(*************"]
            },
            "Fax T": {
                "semantic_meaning": "prescriber_fax",
                "category": "provider_info",
                "field_type": "text",
                "extraction_guidance": "Prescriber fax number",
                "required": False,
                "examples": ["(*************", "(*************"]
            }
        }
        
        logger.info(f"Created fallback schema with {len(fallback_schema)} fields")
        return fallback_schema
    
    def _validate_schema_structure(self, candidate: Dict[str, Any]) -> bool:
        """Validate that the candidate has proper schema structure."""
        
        # Check if at least one field has the expected structure
        for field_id, field_info in candidate.items():
            if isinstance(field_info, dict):
                # Look for expected keys
                expected_keys = ['semantic_meaning', 'category', 'field_type']
                if any(key in field_info for key in expected_keys):
                    return True
        
        return False