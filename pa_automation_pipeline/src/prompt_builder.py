import json
from typing import Dict

def create_dynamic_extraction_prompt(schema: Dict[str, any]) -> str:
    """
    Creates a dynamic, structured prompt for Gemini based on a form's schema.

    Args:
        schema: The schema dictionary loaded from the form's YAML file.

    Returns:
        A detailed prompt string to send to the LLM.
    """
    json_stub = {}
    # Use the human-readable names from the schema to guide the LLM
    for slug, properties in schema.items():
        # Providing a hint for the LLM based on the human-readable name
        json_stub[slug] = f"<{properties.get('human_name', 'data')}>"

    json_format_string = json.dumps(json_stub, indent=2)

    prompt = f"""
You are an expert medical data extraction AI. Your task is to analyze a
referral package and extract specific pieces of information needed to fill out a
Prior Authorization (PA) form.

Analyze the provided referral document and extract the values for the fields
listed in the following JSON structure. The keys in the JSON are clean 'slugs'
and the values are hints based on the form's field names.

**Instructions:**
1.  Read the entire referral document carefully to find the corresponding data.
2.  Fill in the values for each key in the JSON structure below.
3.  **If a piece of information cannot be found, you MUST return `null` for that key.**
4.  Do not invent or infer any information that is not explicitly present.
5.  Your final output MUST BE ONLY a single, valid JSON object and nothing else.

**Desired Output Format:**
```json
{json_format_string}
```
"""
    return prompt
