"""
Intelligent Data Extractor - Uses the visually-analyzed schema to extract data with precision.
This solves the accuracy problem by knowing exactly what each field means.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any

import google.generativeai as genai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentDataExtractor:
    """Extracts data using precise field-by-field guidance from visual schema analysis."""
    
    def __init__(self):
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in .env file.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name="gemini-2.0-flash")
    
    def _upload_file(self, file_path: Path):
        """Upload file to Gemini."""
        logger.info(f"Uploading {file_path.name} for data extraction...")
        uploaded_file = genai.upload_file(path=file_path)
        
        while uploaded_file.state.name == "PROCESSING":
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)
            
        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File upload failed: {uploaded_file.name}")
            
        return uploaded_file
    
    def extract_with_intelligent_schema(self, referral_pdf_path: Path, intelligent_schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract data using the intelligent schema that knows what each field means.
        This ensures accurate data goes to the right places.
        """
        
        try:
            referral_file = self._upload_file(referral_pdf_path)
            
            # Create targeted extraction prompt based on intelligent schema
            extraction_prompt = self._create_precise_extraction_prompt(intelligent_schema)
            
            logger.info("Extracting data with field-specific guidance...")
            response = self.model.generate_content([extraction_prompt, referral_file])
            
            # Parse extracted data with metadata support
            import re
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response.text, re.DOTALL)
            if json_match:
                raw_extracted_data = json.loads(json_match.group(1))
            else:
                # Try direct JSON parsing
                start = response.text.find('{')
                end = response.text.rfind('}') + 1
                if start != -1 and end > start:
                    raw_extracted_data = json.loads(response.text[start:end])
                else:
                    logger.error("Could not parse extracted data")
                    return {}
            
            # Extract values and metadata from new format
            extracted_data, extraction_metadata = self._parse_enhanced_output(raw_extracted_data)
            
            # Validate and clean extracted data
            validated_data = self._validate_extracted_data(extracted_data, intelligent_schema)
            
            non_null_count = sum(1 for v in validated_data.values() if v is not None)
            logger.info(f"Extracted {non_null_count}/{len(validated_data)} fields successfully")
            
            # Return enhanced results with metadata
            return {
                "extracted_data": validated_data,
                "extraction_metadata": extraction_metadata,
                "extraction_summary": {
                    "total_fields": len(validated_data),
                    "extracted_fields": non_null_count,
                    "extraction_rate": f"{non_null_count/len(validated_data)*100:.1f}%"
                }
            }
            
        except Exception as e:
            logger.error(f"Intelligent extraction failed: {e}")
            return {}
        finally:
            if 'referral_file' in locals():
                try:
                    genai.delete_file(referral_file.name)
                except:
                    pass
    
    def _create_precise_extraction_prompt(self, intelligent_schema: Dict[str, Any]) -> str:
        """Create extraction prompt with chain-of-thought reasoning and justification."""
        
        prompt = """# Precise PA Data Extraction with Chain-of-Thought Reasoning

You are extracting data from a medical referral package to fill a Prior Authorization form. You must use a systematic reasoning approach for each field extraction.

## **ENHANCED SYSTEM PROMPT FOR MEDICAL ACCURACY**

You are an expert medical data extraction specialist with deep knowledge of:
- Prior Authorization workflows and requirements
- Medical terminology and clinical documentation
- Insurance verification and member identification
- Prescription drug protocols and dosing guidelines
- Provider credentialing and practice information

**CRITICAL SUCCESS FACTORS**:
1. **Accuracy over completeness** - Better to return null than wrong data
2. **Chain-of-thought reasoning** - Think through each field systematically
3. **Clinical context awareness** - Understand medical relationships
4. **Document structure recognition** - Know where to look for specific data types
5. **Justification required** - Explain your reasoning for each extraction

## **REASONING EXAMPLES**

Here is how to think through field extraction:

**Field:** "prescriber_npi"
**Thought Process:** "The NPI is a 10-digit National Provider Identifier. I will scan for 'NPI' labels on letterheads, signature blocks, and any provider information forms. I found a 10-digit number next to Dr. Hao H Gu's signature on page 5. This appears to be formatted correctly as an NPI."
**Extraction:** "**********"
**Confidence:** "High"
**Page Found:** 5
**Justification:** "Found 10-digit number labeled as NPI next to prescriber signature on letterhead"

**Field:** "insurance_group_number"
**Thought Process:** "The group number is typically found on the insurance card near the member ID. I have located the insurance card on page 3, but I only see a Member ID and a Payer ID. I've scanned the entire insurance section and related forms but cannot locate a field specifically labeled 'Group Number'."
**Extraction:** null
**Confidence:** "High"
**Page Found:** null
**Justification:** "Thoroughly searched insurance card and related documents but no group number field present"

**Field:** "patient_dob"
**Thought Process:** "Patient date of birth should be on the patient demographics sheet or insurance card. I found '04/01/2001' in the 'Date of Birth' field on the patient registration form on page 2. The format matches MM/DD/YYYY standard."
**Extraction:** "04/01/2001"
**Confidence:** "High"
**Page Found:** 2
**Justification:** "Clear DOB field on patient registration form with standard date format"

## **FIELD-BY-FIELD EXTRACTION GUIDE**

Based on visual analysis of the actual PA form, here's what each field requires:

"""
        
        # Add detailed field-by-field instructions
        for field_id, field_info in intelligent_schema.items():
            semantic_meaning = field_info.get('semantic_meaning', field_id)
            category = field_info.get('category', 'unknown')
            extraction_guidance = field_info.get('extraction_guidance', 'Extract this information')
            examples = field_info.get('examples', [])
            field_type = field_info.get('field_type', 'text')
            required = field_info.get('required', False)
            
            status = "🔴 REQUIRED" if required else "🟡 OPTIONAL"
            
            prompt += f"""
### **{field_id}** ({semantic_meaning}) - {category.upper()} {status}
- **Purpose**: {semantic_meaning}
- **How to find**: {extraction_guidance}
- **Field type**: {field_type}
- **Expected format**: {', '.join(examples) if examples else 'Standard format'}
- **Search locations**: {self._get_search_locations(category)}
"""
        
        prompt += """

## **DOCUMENT SEARCH STRATEGY**

### **Patient Demographics** (Pages 1-3)
- Insurance cards (member IDs, group numbers)
- Patient registration forms
- Demographics and contact information

### **Clinical Information** (Throughout document)
- Progress notes with diagnosis codes
- Assessment and plan sections
- Laboratory results and test values
- Medical history and current conditions

### **Medication Details** (Clinical notes and prescriptions)
- Treatment plans and protocols
- Drug names and formulations
- Dosing schedules and administration routes
- Duration and refill information

### **Provider Information** (Letterheads and signatures)
- Physician names and credentials
- NPI numbers (10-digit identifiers)
- Practice contact information
- Billing and administrative details

## **EXTRACTION PROCESS**

For each field in the schema, follow this systematic approach:

1. **State your search strategy**: Where you expect to find this information
2. **Document your search**: What you actually found during your scan
3. **Make your decision**: Extract the value or determine it's missing
4. **Provide justification**: Explain your reasoning and confidence level

## **EXTRACTION VALIDATION RULES**

- **Names**: Verify consistency across documents
- **Dates**: Use MM/DD/YYYY format, validate reasonable ranges
- **ICD-10 Codes**: Format Letter+Numbers (e.g., K50.111, M05.79)
- **NPI Numbers**: Exactly 10 digits
- **Phone Numbers**: Standard US format (XXX) XXX-XXXX
- **Member IDs**: Alphanumeric, varies by payer

## **OUTPUT REQUIREMENTS**

Return a JSON object with the following structure for each field. Use EXACT field IDs from the schema:

```json
{"""
        
        # Add JSON template with field IDs and metadata structure
        field_ids = list(intelligent_schema.keys())
        for i, field_id in enumerate(field_ids):
            comma = "," if i < len(field_ids) - 1 else ""
            prompt += f"""
  "{field_id}": {{
    "value": "extracted_value_or_null",
    "page_found": "page_number_or_null",
    "confidence": "High|Medium|Low",
    "justification": "Brief explanation of extraction reasoning"
  }}{comma}"""
        
        prompt += """
}
```

**CONFIDENCE LEVELS**:
- **High**: Value clearly labeled and unambiguous
- **Medium**: Value found but some interpretation required
- **Low**: Value uncertain or requires assumption

**FINAL INSTRUCTION**: Use chain-of-thought reasoning for each field. For each field, first state where you expect to find the information, then state what you actually found, then provide the extraction with confidence and justification. Extract only what you can find with reasonable confidence. Use null for missing information. Maintain medical accuracy and clinical context throughout."""
        
        return prompt
    
    def _get_search_locations(self, category: str) -> str:
        """Get specific search locations based on field category."""
        
        location_map = {
            "patient_demographics": "Insurance cards, patient registration, contact forms",
            "insurance_info": "Insurance verification, member cards, coverage documents", 
            "clinical_data": "Progress notes, assessment sections, diagnosis lists",
            "medication_info": "Treatment plans, prescription orders, dosing protocols",
            "provider_info": "Letterheads, signatures, practice information, credentials",
            "authorization_info": "PA forms, approval letters, administrative documents"
        }
        
        return location_map.get(category, "Throughout document as appropriate")
    
    def _parse_enhanced_output(self, raw_data: Dict[str, Any]) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """Parse enhanced output format with metadata into separate data and metadata dicts."""
        
        extracted_data = {}
        extraction_metadata = {}
        
        for field_id, field_data in raw_data.items():
            if isinstance(field_data, dict) and "value" in field_data:
                # New enhanced format with metadata
                extracted_data[field_id] = field_data.get("value")
                extraction_metadata[field_id] = {
                    "page_found": field_data.get("page_found"),
                    "confidence": field_data.get("confidence", "Unknown"),
                    "justification": field_data.get("justification", "No justification provided")
                }
            else:
                # Legacy format - just the value
                extracted_data[field_id] = field_data
                extraction_metadata[field_id] = {
                    "page_found": None,
                    "confidence": "Unknown",
                    "justification": "Legacy extraction format"
                }
        
        return extracted_data, extraction_metadata
    
    def _validate_extracted_data(self, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Validate extracted data against schema expectations."""
        
        validated = {}
        
        for field_id, value in extracted_data.items():
            if value is None or str(value).strip() == "" or str(value).lower() == "null":
                validated[field_id] = None
                continue
            
            field_info = schema.get(field_id, {})
            field_type = field_info.get('field_type', 'text')
            category = field_info.get('category', 'unknown')
            
            # Apply field-specific validation
            try:
                validated_value = self._validate_field_value(str(value), category, field_id)
                validated[field_id] = validated_value
            except ValueError as e:
                logger.warning(f"Validation failed for {field_id}: {e}")
                validated[field_id] = str(value)  # Keep original if validation fails
        
        return validated
    
    def _validate_field_value(self, value: str, category: str, field_id: str) -> str:
        """Validate individual field value based on category and field ID."""
        
        value = value.strip()
        
        # Date validation
        if 'dob' in field_id.lower() or 'date' in field_id.lower():
            import re
            if re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', value):
                return value
            elif re.match(r'^\d{4}-\d{2}-\d{2}$', value):
                # Convert YYYY-MM-DD to MM/DD/YYYY
                parts = value.split('-')
                return f"{parts[1]}/{parts[2]}/{parts[0]}"
        
        # Phone/Fax validation
        if 'phone' in field_id.lower() or 'fax' in field_id.lower():
            import re
            digits = re.sub(r'\D', '', value)
            if len(digits) == 10:
                return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
            elif len(digits) == 11 and digits[0] == '1':
                return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        
        # NPI validation
        if 'npi' in field_id.lower():
            import re
            digits = re.sub(r'\D', '', value)
            if len(digits) == 10:
                return digits
            else:
                raise ValueError(f"Invalid NPI format: {value}")
        
        # ICD-10 validation
        if category == 'clinical_data' and ('diagnosis' in field_id.lower() or 'icd' in field_id.lower()):
            import re
            # Look for ICD-10 pattern in the value
            icd_match = re.search(r'[A-TV-Z]\d{2,3}(\.\d{1,4})?', value.upper())
            if icd_match:
                return icd_match.group(0)
        
        # Default: return cleaned value
        return value.strip()
    
    def generate_extraction_report(self, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed extraction report."""
        
        report = {
            "extraction_summary": {
                "total_fields": len(schema),
                "extracted_fields": sum(1 for v in extracted_data.values() if v is not None),
                "extraction_rate": f"{sum(1 for v in extracted_data.values() if v is not None)/len(schema)*100:.1f}%"
            },
            "by_category": {},
            "by_priority": {"required": {"extracted": 0, "total": 0}, "optional": {"extracted": 0, "total": 0}},
            "extracted_values": {},
            "missing_fields": []
        }
        
        # Analyze by category and priority
        for field_id, field_info in schema.items():
            category = field_info.get('category', 'unknown')
            required = field_info.get('required', False)
            value = extracted_data.get(field_id)
            
            # Category analysis
            if category not in report["by_category"]:
                report["by_category"][category] = {"extracted": 0, "total": 0}
            report["by_category"][category]["total"] += 1
            
            if value is not None:
                report["by_category"][category]["extracted"] += 1
                report["extracted_values"][field_id] = value
            else:
                report["missing_fields"].append({
                    "field_id": field_id,
                    "meaning": field_info.get('semantic_meaning', field_id),
                    "category": category,
                    "required": required
                })
            
            # Priority analysis
            priority = "required" if required else "optional"
            report["by_priority"][priority]["total"] += 1
            if value is not None:
                report["by_priority"][priority]["extracted"] += 1
        
        return report