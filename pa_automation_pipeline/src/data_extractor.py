import json
import logging
import os
import re
import time
from pathlib import Path
from typing import Dict, Any

import google.generativeai as genai
from dotenv import load_dotenv

from src.prompt_builder import create_dynamic_extraction_prompt

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataExtractor:
    def __init__(self):
        load_dotenv()
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in .env file.")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name="gemini-2.0-flash")

    def _upload_file(self, file_path: Path) -> Any:
        """Uploads a file to the Gemini API and waits for it to be processed."""
        logger.info(f"Uploading file to Gemini: {file_path.name}")
        uploaded_file = genai.upload_file(path=file_path)
        
        while uploaded_file.state.name == "PROCESSING":
            logger.info("File is processing...")
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)

        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File processing failed: {uploaded_file.name}")
        
        logger.info(f"File uploaded and processed successfully: {uploaded_file.name}")
        return uploaded_file

    def _extract_json_from_response(self, text: str) -> Dict[str, Any]:
        """Safely extracts a JSON object from a string."""
        match = re.search(r"```json\s*(\{.*?\})\s*```", text, re.DOTALL)
        if match:
            json_str = match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON from response: {e}")
                raise ValueError("LLM response did not contain valid JSON.")
        
        # Fallback for plain JSON response
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            raise ValueError("Could not find a valid JSON object in the LLM response.")


    def extract_data_from_referral(self, referral_pdf_path: Path, schema: Dict) -> Dict[str, Any]:
        """
        Orchestrates the data extraction from a referral package using a dynamic prompt.
        """
        if not referral_pdf_path.exists():
            raise FileNotFoundError(f"Referral package not found at {referral_pdf_path}")

        prompt = create_dynamic_extraction_prompt(schema)
        referral_file = self._upload_file(referral_pdf_path)

        logger.info("Sending extraction request to Gemini...")
        response = self.model.generate_content([prompt, referral_file])
        
        logger.info("Received response from Gemini. Parsing JSON...")
        
        try:
            # The response object from gemini-1.5-flash is often multipart.
            # We need to access the text part.
            extracted_data = self._extract_json_from_response(response.text)
            logger.info("Successfully extracted structured data.")
            return extracted_data
        except (ValueError, IndexError) as e:
            logger.error(f"Error processing Gemini response: {e}")
            logger.error(f"Raw response was: {response.text}")
            return {}
        finally:
            # Clean up the uploaded file
            genai.delete_file(referral_file.name)
            logger.info(f"Cleaned up uploaded file: {referral_file.name}")
