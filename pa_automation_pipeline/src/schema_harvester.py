import argparse
import logging
import re
import unicodedata
from pathlib import Path

import yaml
from pypdf import PdfReader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_slug(text: str) -> str:
    """Creates a clean, URL-friendly slug from a string."""
    if not text:
        return ""
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')
    text = re.sub(r'[^\w\s-]', '', text).strip().lower()
    text = re.sub(r'[-\s]+', '_', text)
    return text

def harvest_schema(pdf_path: Path) -> dict:
    """
    Analyzes a PDF form and extracts a schema of its fillable fields.
    """
    logger.info(f"Harvesting schema from '{pdf_path}'...")
    schema = {}
    try:
        reader = PdfReader(pdf_path)
        fields = reader.get_fields()

        if not fields:
            logger.warning(f"No AcroForm widgets found in '{pdf_path}'. It may be a non-interactive PDF.")
            return {}

        for field_name, properties in fields.items():
            acro_id = field_name
            # /T is the human-readable field name (tooltip)
            # Explicitly convert the PyPDF2 object to a string to prevent YAML errors
            human_readable_name = str(properties.get('/T', ''))

            if not human_readable_name:
                logger.warning(f"Skipping field with no human-readable name: {acro_id}")
                continue

            slug = generate_slug(human_readable_name)
            if not slug:
                slug = generate_slug(acro_id) # Fallback to a slug of the acro_id

            field_type = "text"
            if properties.get('/FT') == '/Btn':
                field_type = "checkbox"
            
            schema[slug] = {
                "acro_id": acro_id,
                "human_name": human_readable_name,
                "type": field_type,
            }
        
        logger.info(f"Successfully discovered {len(schema)} fields from '{pdf_path}'.")
        return schema

    except Exception as e:
        logger.error(f"Failed to harvest schema from '{pdf_path}': {e}")
        return {}

def main():
    parser = argparse.ArgumentParser(description="Harvest a schema from a fillable PDF form.")
    parser.add_argument("--pdf", type=Path, required=True, help="Path to the blank PA form PDF.")
    parser.add_argument("--output_dir", type=Path, required=True, help="Directory to save the generated YAML schema file.")
    args = parser.parse_args()

    args.output_dir.mkdir(parents=True, exist_ok=True)
    
    schema_data = harvest_schema(args.pdf)

    if schema_data:
        # Create a unique name for the schema based on the PDF filename
        schema_filename = args.pdf.stem + "_schema.yaml"
        output_path = args.output_dir / schema_filename
        
        with open(output_path, 'w') as f:
            yaml.dump(schema_data, f, indent=2, sort_keys=True)
        print(f"✅ Schema successfully saved to: {output_path}")

if __name__ == "__main__":
    main()
