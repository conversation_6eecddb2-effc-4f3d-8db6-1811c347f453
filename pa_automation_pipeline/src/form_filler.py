import logging
from pathlib import Path
from typing import Dict, Any

from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fill_pa_form(
    blank_pdf_path: Path,
    schema: Dict[str, Any],
    extracted_data: Dict[str, Any],
    output_path: Path
) -> bool:
    """
    Fills a PA form using the schema and extracted data.

    Args:
        blank_pdf_path: Path to the blank PA form PDF.
        schema: The dictionary schema for the form.
        extracted_data: The JSON data extracted from the referral.
        output_path: Path to save the filled PDF.

    Returns:
        True if the form was filled and saved, False otherwise.
    """
    logger.info(f"Starting to fill PDF form: {blank_pdf_path.name}")
    
    try:
        reader = PdfReader(blank_pdf_path)
        writer = PdfWriter()
        writer.clone_document_from_reader(reader)

        form_values = {}
        missing_fields = []

        for slug, schema_props in schema.items():
            # The dictionary key (slug) is often the PDF field ID (e.g. "T12").
            # We actually want to map using the semantic slug (e.g. "patient_first_name")
            acro_id = schema_props.get("acro_id", slug)  # fall back to slug if acro_id not provided

            # Prefer the semantic name for lookup; if not present, fall back to the dict key
            semantic_slug = schema_props.get("semantic_meaning", slug)
            value = extracted_data.get(semantic_slug)

            if value is not None and str(value).strip():
                # Handle checkboxes
                if schema_props.get("type") == "checkbox":
                    # Simple "Yes" for any truthy value. Can be made more complex.
                    form_values[acro_id] = "/Yes"
                else:
                    form_values[acro_id] = str(value)
            else:
                missing_fields.append(schema_props.get("human_name", slug))

        if not form_values:
            logger.warning("No data was extracted to fill the form. Output will be a copy of the blank form.")
        else:
            logger.info(f"Attempting to fill {len(form_values)} fields...")
            for page in writer.pages:
                try:
                    writer.update_page_form_field_values(page, form_values)
                except Exception as e:
                    logger.error(f"Could not update fields on a page: {e}")
            
            # This is critical to make the values visible in most viewers
            writer.set_need_appearances_writer()
            logger.info("Set 'need_appearances' flag to True.")

        # Save the filled PDF
        with open(output_path, "wb") as f:
            writer.write(f)
        
        logger.info(f"✅ Successfully created filled PDF: {output_path}")

        # Save the missing fields report
        report_path = output_path.with_suffix('.md')
        with open(report_path, 'w') as f:
            f.write("# Missing Information Report\n\n")
            if missing_fields:
                f.write("The following fields could not be found in the referral package:\n")
                for field in sorted(missing_fields):
                    f.write(f"- {field}\n")
            else:
                f.write("All fields were successfully found and populated.\n")
        logger.info(f"✅ Successfully created missing fields report: {report_path}")

        return True

    except Exception as e:
        logger.error(f"A critical error occurred while filling the PDF: {e}", exc_info=True)
        return False
