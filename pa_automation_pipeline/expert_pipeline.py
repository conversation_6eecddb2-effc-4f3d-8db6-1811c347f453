#!/usr/bin/env python3
"""
Expert PA Automation Pipeline using Expert Medical Extractor
Runs the complete pipeline with medical-grade extraction accuracy.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any

from src.intelligent_schema_generator import IntelligentSchemaGenerator
from src.form_filler import fill_pa_form
from expert_medical_extractor import ExpertMedicalExtractor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExpertPAPipeline:
    """Complete PA automation pipeline with expert medical extraction."""
    
    def __init__(self):
        self.schema_generator = IntelligentSchemaGenerator()
        self.medical_extractor = ExpertMedicalExtractor()
    
    def run_patient_pipeline(self, patient_name: str, pa_form_path: Path, referral_path: Path, output_dir: Path) -> Dict[str, Any]:
        """
        Run complete pipeline for a patient.
        
        Args:
            patient_name: Patient identifier (e.g., "<PERSON>", "Akshay")
            pa_form_path: Path to blank PA form
            referral_path: Path to referral package
            output_dir: Directory for outputs
            
        Returns:
            Pipeline results summary
        """
        
        logger.info(f"🏥 STARTING EXPERT PIPELINE FOR {patient_name.upper()}")
        logger.info("=" * 80)
        
        results = {
            "patient_name": patient_name,
            "schema_generation": {},
            "data_extraction": {},
            "form_filling": {},
            "overall_success": False
        }
        
        try:
            # Step 1: Generate intelligent schema from PA form
            logger.info("📋 STEP 1: Generating intelligent schema from PA form...")
            schema_result = self.schema_generator.analyze_pa_form_visually(pa_form_path)
            
            if not schema_result:
                logger.error("❌ Schema generation failed")
                results["schema_generation"]["status"] = "FAILED"
                return results
            
            # Save schema for debugging
            schema_path = output_dir / f"{patient_name}_expert_schema.json"
            with open(schema_path, 'w') as f:
                json.dump(schema_result, f, indent=2)
            
            # Filter out metadata fields to count actual form fields
            form_fields = {k: v for k, v in schema_result.items() 
                          if isinstance(v, dict) and 'semantic_meaning' in v}
            field_count = len(form_fields)
            
            logger.info(f"✅ Schema generated: {field_count} fields identified")
            results["schema_generation"] = {
                "status": "SUCCESS",
                "fields_identified": field_count,
                "schema_path": str(schema_path)
            }
            
            # Step 2: Extract data using expert medical extractor
            logger.info("🔬 STEP 2: Expert medical data extraction...")
            # Convert schema to format expected by medical extractor
            medical_schema = {"fields": form_fields}
            extraction_result = self.medical_extractor.extract_medical_data(referral_path, medical_schema)
            
            if 'error' in extraction_result:
                logger.error(f"❌ Medical extraction failed: {extraction_result['error']}")
                results["data_extraction"]["status"] = "FAILED"
                return results
            
            extracted_data = extraction_result['extracted_data']
            extraction_rate = extraction_result['extraction_rate']
            
            # Save extracted data for debugging
            extraction_path = output_dir / f"{patient_name}_expert_extracted.json"
            with open(extraction_path, 'w') as f:
                json.dump(extraction_result, f, indent=2)
            
            logger.info(f"✅ Medical extraction: {extraction_rate} extraction rate")
            results["data_extraction"] = {
                "status": "SUCCESS",
                "extraction_rate": extraction_rate,
                "fields_extracted": extraction_result['extraction_count'],
                "total_fields": extraction_result['total_fields'],
                "data_path": str(extraction_path)
            }
            
            # Step 3: Fill PA form with extracted data
            logger.info("📝 STEP 3: Filling PA form with expert-extracted data...")
            filled_pdf_path = output_dir / f"{patient_name}_EXPERT_FILLED.pdf"
            
            # Create direct field ID mapping - extracted data has field IDs as keys
            # The form filler expects schema keys to match extracted data keys
            field_schema = {}
            for field_id, field_info in form_fields.items():
                # Use field_id as the schema key since extracted_data has field_id keys
                field_schema[field_id] = {
                    "acro_id": field_id,
                    "semantic_meaning": field_id,  # Make semantic_meaning same as field_id
                    "type": field_info.get('field_type', 'text'),
                    "human_name": field_info.get('semantic_meaning', field_id)
                }
            
            fill_success = fill_pa_form(
                blank_pdf_path=pa_form_path,
                schema=field_schema,
                extracted_data=extracted_data,
                output_path=filled_pdf_path
            )
            
            if fill_success:
                # Count filled fields
                filled_count = sum(1 for v in extracted_data.values() if v is not None)
                total_fields = len(extracted_data)
                fill_rate = f"{filled_count}/{total_fields}"
                
                logger.info(f"✅ Form filling: {fill_rate} fields filled successfully")
                results["form_filling"] = {
                    "status": "SUCCESS",
                    "fields_filled": filled_count,
                    "total_fields": total_fields,
                    "fill_rate": fill_rate,
                    "output_path": str(filled_pdf_path)
                }
                results["overall_success"] = True
            else:
                logger.error("❌ Form filling failed")
                results["form_filling"]["status"] = "FAILED"
                return results
            
            # Generate final report
            self._generate_pipeline_report(results, output_dir, patient_name)
            
            logger.info("=" * 80)
            logger.info(f"🎉 EXPERT PIPELINE COMPLETED SUCCESSFULLY FOR {patient_name.upper()}")
            logger.info(f"📄 Filled form: {filled_pdf_path}")
            logger.info("=" * 80)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Pipeline failed for {patient_name}: {e}")
            results["error"] = str(e)
            return results
    
    def _generate_pipeline_report(self, results: Dict[str, Any], output_dir: Path, patient_name: str):
        """Generate comprehensive pipeline report."""
        
        report_path = output_dir / f"{patient_name}_EXPERT_PIPELINE_REPORT.md"
        
        with open(report_path, 'w') as f:
            f.write(f"# Expert PA Pipeline Report - {patient_name}\n\n")
            
            f.write("## Pipeline Summary\n\n")
            f.write(f"- **Patient**: {patient_name}\n")
            f.write(f"- **Overall Success**: {'✅ YES' if results['overall_success'] else '❌ NO'}\n")
            f.write(f"- **Generated**: {results.get('timestamp', 'Unknown')}\n\n")
            
            f.write("## Step Results\n\n")
            
            # Schema generation
            schema = results.get("schema_generation", {})
            f.write("### 1. Schema Generation\n")
            f.write(f"- **Status**: {schema.get('status', 'Unknown')}\n")
            f.write(f"- **Fields Identified**: {schema.get('fields_identified', 0)}\n")
            f.write(f"- **Schema File**: {schema.get('schema_path', 'N/A')}\n\n")
            
            # Data extraction
            extraction = results.get("data_extraction", {})
            f.write("### 2. Expert Medical Extraction\n")
            f.write(f"- **Status**: {extraction.get('status', 'Unknown')}\n")
            f.write(f"- **Extraction Rate**: {extraction.get('extraction_rate', 'N/A')}\n")
            f.write(f"- **Fields Extracted**: {extraction.get('fields_extracted', 0)}/{extraction.get('total_fields', 0)}\n")
            f.write(f"- **Data File**: {extraction.get('data_path', 'N/A')}\n\n")
            
            # Form filling
            filling = results.get("form_filling", {})
            f.write("### 3. Form Filling\n")
            f.write(f"- **Status**: {filling.get('status', 'Unknown')}\n")
            f.write(f"- **Fields Filled**: {filling.get('fill_rate', 'N/A')}\n")
            f.write(f"- **Output PDF**: {filling.get('output_path', 'N/A')}\n\n")
            
            if results['overall_success']:
                f.write("## ✅ Pipeline Success\n\n")
                f.write("The expert PA automation pipeline completed successfully with medical-grade accuracy.\n")
            else:
                f.write("## ❌ Pipeline Issues\n\n")
                f.write("The pipeline encountered issues. Check individual step statuses above.\n")
        
        logger.info(f"📊 Pipeline report saved: {report_path}")

def run_abdullah_pipeline():
    """Run expert pipeline for Abdullah."""
    
    pipeline = ExpertPAPipeline()
    
    # Abdullah's files (note: directory is "Adbulla" with one 'l')
    pa_form = Path("Input Data/Adbulla/PA.pdf")
    referral = Path("Input Data/Adbulla/referral_package.pdf")
    output_dir = Path("expert_output")
    output_dir.mkdir(exist_ok=True)
    
    if not pa_form.exists() or not referral.exists():
        logger.error(f"❌ Missing files for Abdullah:")
        logger.error(f"   PA form: {pa_form} ({'exists' if pa_form.exists() else 'missing'})")
        logger.error(f"   Referral: {referral} ({'exists' if referral.exists() else 'missing'})")
        return None
    
    return pipeline.run_patient_pipeline("Abdullah", pa_form, referral, output_dir)

def run_akshay_pipeline():
    """Run expert pipeline for Akshay."""
    
    pipeline = ExpertPAPipeline()
    
    # Akshay's files
    pa_form = Path("Input Data/Akshay/pa.pdf")
    referral = Path("Input Data/Akshay/referral_package.pdf")
    output_dir = Path("expert_output")
    output_dir.mkdir(exist_ok=True)
    
    if not pa_form.exists() or not referral.exists():
        logger.error(f"❌ Missing files for Akshay:")
        logger.error(f"   PA form: {pa_form} ({'exists' if pa_form.exists() else 'missing'})")
        logger.error(f"   Referral: {referral} ({'exists' if referral.exists() else 'missing'})")
        return None
    
    return pipeline.run_patient_pipeline("Akshay", pa_form, referral, output_dir)

def run_both_pipelines():
    """Run expert pipeline for both Abdullah and Akshay."""
    
    print("\n" + "=" * 100)
    print("🏥 EXPERT PA AUTOMATION PIPELINE - PRODUCTION RUN")
    print("=" * 100)
    
    results = {}
    
    # Run Abdullah
    print("\n🔸 Running Abdullah pipeline...")
    abdullah_result = run_abdullah_pipeline()
    results["Abdullah"] = abdullah_result
    
    # Run Akshay
    print("\n🔸 Running Akshay pipeline...")
    akshay_result = run_akshay_pipeline()
    results["Akshay"] = akshay_result
    
    # Summary
    print("\n" + "=" * 100)
    print("📊 PIPELINE SUMMARY")
    print("=" * 100)
    
    for patient, result in results.items():
        if result:
            status = "✅ SUCCESS" if result.get('overall_success', False) else "❌ FAILED"
            extraction_rate = result.get('data_extraction', {}).get('extraction_rate', 'N/A')
            fill_rate = result.get('form_filling', {}).get('fill_rate', 'N/A')
            
            print(f"{patient:>10}: {status}")
            print(f"{'':>10}  Extraction: {extraction_rate}")
            print(f"{'':>10}  Filling: {fill_rate}")
        else:
            print(f"{patient:>10}: ❌ FAILED (Missing files)")
        print()
    
    return results

if __name__ == "__main__":
    results = run_both_pipelines()