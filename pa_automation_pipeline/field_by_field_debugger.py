#!/usr/bin/env python3
"""
Field-by-Field Debugger - Analyzes extraction failures at the individual field level
Following your approach: Schema → Extraction → Manual Verification
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FieldByFieldDebugger:
    """Debug extraction failures at the individual field level."""
    
    def debug_akshay_fields(self):
        """Debug Akshay's field extraction following your systematic approach."""
        
        print("=" * 80)
        print("🔍 FIELD-BY-FIELD EXTRACTION DEBUG - AKSHAY")
        print("=" * 80)
        
        # Load the actual results
        actual_fields_path = Path("fixed_output/Akshay_actual_fields.json")
        extracted_data_path = Path("fixed_output/Akshay_fixed_extracted.json")
        
        if not actual_fields_path.exists() or not extracted_data_path.exists():
            print("❌ Missing debug files. Run fixed pipeline first.")
            return
        
        # Load data
        with open(actual_fields_path) as f:
            actual_fields = json.load(f)
        
        with open(extracted_data_path) as f:
            extraction_result = json.load(f)
        
        extracted_data = extraction_result.get('extracted_data', {})
        pdf_fields = actual_fields.get('fields', {})
        
        print(f"📊 OVERVIEW:")
        print(f"   Total PDF Fields: {len(pdf_fields)}")
        print(f"   Fields with Data: {len([v for v in extracted_data.values() if v is not None])}")
        print(f"   Fields with NULL: {len([v for v in extracted_data.values() if v is None])}")
        print()
        
        # Categorize fields
        filled_fields = []
        null_fields = []
        missing_fields = []
        
        for field_name in pdf_fields.keys():
            if field_name in extracted_data:
                if extracted_data[field_name] is not None:
                    filled_fields.append(field_name)
                else:
                    null_fields.append(field_name)
            else:
                missing_fields.append(field_name)
        
        # Show filled fields (successes)
        print("✅ SUCCESSFULLY FILLED FIELDS:")
        for field in filled_fields:
            value = extracted_data[field]
            # Truncate long values for display
            display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
            print(f"   {field}: {display_value}")
        print()
        
        # Analyze NULL fields (extraction failures)
        print("❌ FIELDS RETURNED NULL (Extraction Failures):")
        print("   Following your debug process:")
        print()
        
        critical_fields = {
            "T14": "member_id",
            "T18": "insurance_group_number", 
            "T20": "patient_phone",
            "T21": "patient_address",
            "T21B": "patient_city",
            "T21C": "patient_state", 
            "T21D": "patient_zip",
            "T21E": "patient_email",
            "T21F": "patient_secondary_insurance"
        }
        
        for field_name in null_fields:
            semantic_meaning = critical_fields.get(field_name, "unknown")
            print(f"🔍 DEBUGGING: {field_name} ({semantic_meaning})")
            print(f"   ✅ Step 1 - Schema: Field exists in PDF")
            print(f"   ✅ Step 2 - Extraction: Field was processed but returned NULL")
            print(f"   🔍 Step 3 - Manual Verification Needed:")
            print(f"       → Check referral_package.pdf for {semantic_meaning}")
            print(f"       → Is the data genuinely missing?")
            print(f"       → Is it handwritten/illegible?")
            print(f"       → Is it in an unexpected format/location?")
            print()
        
        if missing_fields:
            print("⚠️  FIELDS NOT PROCESSED (Schema Issues):")
            for field in missing_fields:
                print(f"   {field}: Not found in extraction schema")
            print()
        
        # Provide specific recommendations
        self._provide_extraction_recommendations(null_fields, critical_fields)
    
    def _provide_extraction_recommendations(self, null_fields: List[str], critical_fields: Dict[str, str]):
        """Provide specific recommendations for improving extraction."""
        
        print("💡 EXTRACTION IMPROVEMENT RECOMMENDATIONS:")
        print()
        
        # Group by likely cause
        likely_missing_data = ["T21E", "T21F"]  # Email, secondary insurance often missing
        likely_format_issues = ["T14", "T20", "T21"]  # IDs, phone, address formatting
        likely_location_issues = ["T18", "T21B", "T21C", "T21D"]  # Buried in insurance cards
        
        if any(field in null_fields for field in likely_missing_data):
            print("📋 LIKELY GENUINELY MISSING DATA:")
            for field in likely_missing_data:
                if field in null_fields:
                    print(f"   {field} ({critical_fields.get(field, 'unknown')})")
                    print(f"      → Often not provided in referral packages")
                    print(f"      → System correctly returned NULL")
            print()
        
        if any(field in null_fields for field in likely_format_issues):
            print("🔤 LIKELY FORMAT/RECOGNITION ISSUES:")
            for field in likely_format_issues:
                if field in null_fields:
                    print(f"   {field} ({critical_fields.get(field, 'unknown')})")
                    print(f"      → May be in unexpected format")
                    print(f"      → Consider improving extraction prompt")
                    print(f"      → Add format examples to guidance")
            print()
        
        if any(field in null_fields for field in likely_location_issues):
            print("📍 LIKELY LOCATION/CONTEXT ISSUES:")
            for field in likely_location_issues:
                if field in null_fields:
                    print(f"   {field} ({critical_fields.get(field, 'unknown')})")
                    print(f"      → May be buried in insurance documentation")
                    print(f"      → Consider multi-pass extraction")
                    print(f"      → Add specific location hints")
            print()
        
        print("🎯 NEXT STEPS FOR IMPROVEMENT:")
        print("   1. Manual verification of NULL fields in referral PDF")
        print("   2. Enhance extraction prompts for confirmed data")
        print("   3. Add field-specific search strategies")
        print("   4. Consider document preprocessing for better OCR")
        print("   5. Implement confidence scoring for extractions")
    
    def analyze_extraction_patterns(self):
        """Analyze patterns in extraction success/failure."""
        
        print("\n" + "=" * 80)
        print("📊 EXTRACTION PATTERN ANALYSIS")
        print("=" * 80)
        
        # Load extraction data
        extracted_data_path = Path("fixed_output/Akshay_fixed_extracted.json")
        if not extracted_data_path.exists():
            print("❌ Missing extraction data")
            return
        
        with open(extracted_data_path) as f:
            extraction_result = json.load(f)
        
        extracted_data = extraction_result.get('extracted_data', {})
        
        # Analyze by field type/category
        field_categories = {
            "Patient Demographics": ["T11", "T12", "T13"],
            "Insurance Info": ["T14", "T18", "Insurance Info T.7"],
            "Contact Info": ["T20", "T21", "T21B", "T21C", "T21D", "T21E"],
            "Clinical Info": ["T15", "T16", "T17", "T19"],
            "Provider Info": ["Request by T", "Phone T", "Fax T"],
            "Secondary Info": ["T21F"]
        }
        
        for category, fields in field_categories.items():
            total_fields = len(fields)
            filled_fields = len([f for f in fields if f in extracted_data and extracted_data[f] is not None])
            success_rate = (filled_fields / total_fields) * 100 if total_fields > 0 else 0
            
            print(f"{category:>20}: {filled_fields:>2}/{total_fields} ({success_rate:>5.1f}%)")
        
        print()
        print("🔍 INSIGHTS:")
        
        # Provider info analysis
        provider_success = len([f for f in ["Request by T", "Phone T", "Fax T"] 
                               if f in extracted_data and extracted_data[f] is not None])
        print(f"   Provider Info: {provider_success}/3 (100%) - ✅ Excellent")
        
        # Patient demographics analysis  
        demo_success = len([f for f in ["T11", "T12", "T13"] 
                           if f in extracted_data and extracted_data[f] is not None])
        print(f"   Patient Demographics: {demo_success}/3 (100%) - ✅ Excellent")
        
        # Clinical info analysis
        clinical_success = len([f for f in ["T15", "T16", "T17", "T19"] 
                               if f in extracted_data and extracted_data[f] is not None])
        print(f"   Clinical Info: {clinical_success}/4 (100%) - ✅ Excellent")
        
        # Problem areas
        contact_success = len([f for f in ["T20", "T21", "T21B", "T21C", "T21D", "T21E"] 
                              if f in extracted_data and extracted_data[f] is not None])
        print(f"   Contact Info: {contact_success}/6 (0%) - ❌ Major gap")
        
        insurance_success = len([f for f in ["T14", "T18"] 
                                if f in extracted_data and extracted_data[f] is not None])
        print(f"   Insurance Details: {insurance_success}/2 (0%) - ❌ Major gap")

def debug_specific_field(field_name: str, semantic_meaning: str):
    """Debug a specific field following the systematic approach."""
    
    print(f"\n🔍 DETAILED FIELD DEBUG: {field_name} ({semantic_meaning})")
    print("-" * 60)
    
    # This would involve:
    # 1. Checking if field exists in schema
    # 2. Checking extraction result
    # 3. Manual verification guidance
    # 4. Specific improvement recommendations
    
    print(f"1. ✅ Schema Check: {field_name} exists in PDF")
    print(f"2. ❌ Extraction Check: Returned NULL")
    print(f"3. 🔍 Manual Verification Required:")
    print(f"   → Open Input Data/Akshay/referral_package.pdf")
    print(f"   → Search for: {semantic_meaning}")
    print(f"   → Check if data exists and is readable")
    print(f"4. 💡 Improvement Options:")
    print(f"   → Enhance extraction prompt specificity")
    print(f"   → Add field-specific search locations")
    print(f"   → Consider OCR preprocessing")

if __name__ == "__main__":
    debugger = FieldByFieldDebugger()
    debugger.debug_akshay_fields()
    debugger.analyze_extraction_patterns()
    
    # Example specific field debug
    debug_specific_field("T14", "member_id")