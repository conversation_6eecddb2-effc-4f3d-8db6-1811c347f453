#!/usr/bin/env python3
"""
Find which actual PDF fields correspond to patient demographics (T11, T12, T13)
"""

import json
from pypdf import PdfReader
from pathlib import Path

def analyze_patient_demographics_mapping():
    """Find patient demographics field mapping."""
    
    print("=" * 80)
    print("🔍 PATIENT DEMOGRAPHICS FIELD MAPPING ANALYSIS")
    print("=" * 80)
    
    # Get actual PDF fields
    try:
        reader = PdfReader("Input Data/Akshay/pa.pdf")
        actual_fields = {}
        
        for page_num, page in enumerate(reader.pages):
            if '/Annots' in page:
                annotations = page['/Annots']
                for annotation in annotations:
                    annotation_obj = annotation.get_object()
                    if '/T' in annotation_obj:
                        field_name = annotation_obj['/T']
                        actual_fields[str(field_name)] = f'Page {page_num + 1}'
        
        # Look for fields that might be patient demographics
        patient_field_candidates = []
        
        # Common patterns for patient fields
        patient_patterns = [
            'patient', 'name', 'first', 'last', 'dob', 'birth', 'date',
            'member', 'id', 'insurance', 'subscriber'
        ]
        
        print(f"\n🔍 SEARCHING FOR PATIENT DEMOGRAPHIC FIELDS:")
        print(f"Total PDF fields: {len(actual_fields)}")
        
        # Search by field name patterns
        for field_name in sorted(actual_fields.keys()):
            field_lower = field_name.lower()
            for pattern in patient_patterns:
                if pattern in field_lower:
                    patient_field_candidates.append(field_name)
                    break
        
        print(f"\n📋 POTENTIAL PATIENT FIELDS ({len(patient_field_candidates)}):")
        for field in patient_field_candidates:
            print(f"  - {field}")
        
        # Let's also look at the "Indicate" section which might have patient info
        indicate_fields = [f for f in actual_fields.keys() if f.startswith('Indicate')]
        print(f"\n📄 'INDICATE' SECTION FIELDS ({len(indicate_fields)}):")
        for field in indicate_fields:
            print(f"  - {field}")
        
        # Look at insurance info fields
        insurance_fields = [f for f in actual_fields.keys() if 'Insurance Info' in f]
        print(f"\n💳 INSURANCE INFO FIELDS ({len(insurance_fields)}):")
        for field in insurance_fields:
            print(f"  - {field}")
        
        # Load what was extracted for T11, T12, T13
        extracted_path = Path("expert_output/Akshay_expert_extracted.json")
        if extracted_path.exists():
            with open(extracted_path) as f:
                extracted_data = json.load(f)
            
            missing_fields = ['T11', 'T12', 'T13']
            extracted_values = extracted_data.get('extracted_data', {})
            
            print(f"\n❌ MISSING SCHEMA MAPPINGS:")
            for field in missing_fields:
                if field in extracted_values:
                    value = extracted_values[field]
                    print(f"  {field}: '{value}' (extracted but no PDF field to fill)")
            
            print(f"\n💡 LIKELY FIELD MAPPINGS:")
            print(f"  T11 (first name: 'Akshay') -> probably 'Indicate T.2' or similar patient name field")
            print(f"  T12 (last name: 'chaudhari') -> probably 'Indicate T.3' or similar patient name field") 
            print(f"  T13 (DOB: '02/17/1987') -> probably 'Indicate T.4' or similar patient DOB field")
            
            print(f"\n🎯 RECOMMENDED FIXES:")
            print(f"  1. Update schema to map:")
            print(f"     - T11 -> 'Indicate T.2' (if that's first name)")
            print(f"     - T12 -> 'Indicate T.3' (if that's last name)")
            print(f"     - T13 -> 'Indicate T.4' (if that's DOB)")
            print(f"  2. Or use visual analysis to determine correct mappings")
            print(f"  3. The schema generator should read actual PDF field names first")
        
        return {
            "total_fields": len(actual_fields),
            "patient_candidates": len(patient_field_candidates),
            "indicate_fields": len(indicate_fields),
            "insurance_fields": len(insurance_fields)
        }
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    result = analyze_patient_demographics_mapping()
    if result:
        print(f"\n📊 SUMMARY:")
        print(f"  - {result['patient_candidates']} potential patient demographic fields found")
        print(f"  - {result['indicate_fields']} fields in 'Indicate' section (likely patient info)")
        print(f"  - {result['insurance_fields']} insurance-related fields")