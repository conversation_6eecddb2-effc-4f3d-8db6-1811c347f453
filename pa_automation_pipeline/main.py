import argparse
import json
import logging
from pathlib import Path
import yaml

from src.schema_harvester import harvest_schema
from src.data_extractor import DataExtractor
from src.form_filler import fill_pa_form

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_patient_folder(patient_dir: Path, schemas_dir: Path, output_dir: Path, extractor: DataExtractor):
    """
    Processes a single patient's folder: harvests schema, extracts data, and fills the form.
    """
    logger.info(f"--- Processing patient folder: {patient_dir.name} ---")
    
    pa_form_path = patient_dir / "PA.pdf"
    referral_path = patient_dir / "referral_package.pdf"

    if not pa_form_path.exists() or not referral_path.exists():
        logger.error(f"Missing PA.pdf or referral_package.pdf in {patient_dir}. Skipping.")
        return

    # 1. Harvest Schema
    schema_path = schemas_dir / f"{pa_form_path.stem}_schema.yaml"
    
    # Force delete the schema to ensure a fresh one is always created
    if schema_path.exists():
        logger.warning(f"Deleting existing schema to prevent caching issues: {schema_path}")
        schema_path.unlink()

    logger.info(f"Generating new schema for {pa_form_path.name}...")
    schema_data = harvest_schema(pa_form_path)
    if not schema_data:
        logger.error(f"Could not generate a schema for {pa_form_path.name}. Cannot proceed.")
        return
    with open(schema_path, 'w') as f:
        yaml.dump(schema_data, f, indent=2, sort_keys=True)
    logger.info(f"Schema saved to {schema_path}")

    # 2. Extract Data
    logger.info("Extracting data from referral package...")
    extracted_data = extractor.extract_data_from_referral(referral_path, schema_data)
    if not extracted_data:
        logger.error("Data extraction failed. No data was returned from the LLM.")
        return
        
    # Optional: Save extracted data for debugging
    extracted_data_path = output_dir / f"{patient_dir.name}_extracted_data.json"
    with open(extracted_data_path, 'w') as f:
        json.dump(extracted_data, f, indent=2)
    logger.info(f"Extracted data saved to {extracted_data_path}")

    # 3. Fill Form
    logger.info("Filling PA form with extracted data...")
    filled_pdf_path = output_dir / f"FILLED_{patient_dir.name}_{pa_form_path.name}"
    fill_pa_form(pa_form_path, schema_data, extracted_data, filled_pdf_path)

    logger.info(f"--- Finished processing patient folder: {patient_dir.name} ---")


def main():
    # Get the absolute path of the script's directory
    PIPELINE_ROOT = Path(__file__).parent.resolve()

    parser = argparse.ArgumentParser(description="End-to-end PA Form Automation Pipeline.")
    parser.add_argument("--input_dir", type=Path, default=PIPELINE_ROOT / "Input Data", help="Directory containing patient folders.")
    parser.add_argument("--schemas_dir", type=Path, default=PIPELINE_ROOT / "schemas", help="Directory to store and cache form schemas.")
    parser.add_argument("--output_dir", type=Path, default=PIPELINE_ROOT / "output", help="Directory to save filled forms and reports.")
    args = parser.parse_args()

    # Create directories if they don't exist
    args.schemas_dir.mkdir(parents=True, exist_ok=True)
    args.output_dir.mkdir(parents=True, exist_ok=True)

    # Initialize the data extractor once
    try:
        extractor = DataExtractor()
    except ValueError as e:
        logger.error(f"Failed to initialize Data Extractor: {e}")
        return

    # Process each patient folder in the input directory
    for patient_dir in sorted(args.input_dir.iterdir()):
        if patient_dir.is_dir():
            process_patient_folder(patient_dir, args.schemas_dir, args.output_dir, extractor)

    logger.info("Pipeline run complete.")

if __name__ == "__main__":
    main()
